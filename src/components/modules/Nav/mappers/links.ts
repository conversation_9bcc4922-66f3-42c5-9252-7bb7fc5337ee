import {
  ActionType,
  LinkType,
  NAV_TARGETS,
} from '~/components/modules/Nav/Nav.types';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { checkAafesCIDinCookie } from '~/lib/utils/aafes/aafes-auth';
import { loStorage } from '~/lib/utils/browser-storage';
import { isOTSDeployment, isSimpleShopDeployment } from '~/lib/utils/deploy';
import { checkSSOTokenInCookie, getSSOLoginURL } from '~/lib/utils/sso';
import { ui } from '~/lib/utils/ui-dictionary';

import { NavLocationIcon } from '../NavIcon/NavIcon';

export const dealsLink = {
  href: ROUTE_MAP[ROUTES.DEALS],
  isExternal: false,
  text: ui('links.deals'),
};

export const simpleBusinessLink = {
  href: ROUTE_MAP[ROUTES.SIMPLEBUSINESS],
  isExternal: false,
  text: ui('links.simpleBusiness'),
};

const textLinks = isSimpleShopDeployment()
  ? [dealsLink, simpleBusinessLink]
  : [
      { target: NAV_TARGETS.BROWSE_TIRES, text: ui('links.browseTires') },
      dealsLink,
      simpleBusinessLink,
      { target: NAV_TARGETS.LEARN, text: ui('links.learn') },
    ];

const iconLinks = isSimpleShopDeployment()
  ? []
  : [
      {
        InlineIconComponent: NavLocationIcon,
        label: ui('links.location'),
        target: NAV_TARGETS.LOCATION,
        testId: 'nav-location-button',
        text: '',
      },
    ];

export const navLinks = [...textLinks, ...iconLinks];

export const dealerTireLinks = [...iconLinks];

export function generateAccountLinks() {
  const aafesUser = checkAafesCIDinCookie();
  if (aafesUser || isOTSDeployment()) {
    return [
      {
        href: ROUTE_MAP[ROUTES.ORDER_TRACKING],
        isExternal: false,
        text: ui('links.orderTracking'),
      },
    ];
  } else {
    const isUserLoggedIn = checkSSOTokenInCookie();
    const path = loStorage.getItem('sso_redirect_last_url') ?? '/';
    const accountLinks = [
      {
        href: getSSOLoginURL(true, path),
        isExternal: false,
        text: ui('links.account'),
      },
      {
        href: isUserLoggedIn
          ? ROUTE_MAP[ROUTES.MY_ORDERS]
          : ROUTE_MAP[ROUTES.ORDER_TRACKING],
        isExternal: false,
        text: ui('links.orderTracking'),
      },
    ];

    if (isUserLoggedIn) {
      accountLinks.push({
        href: '/',
        isExternal: false,
        text: ui('links.logout'),
      });
    }
    return accountLinks;
  }
}

export function buildSubNavLinks({ isMobile }: { isMobile: boolean }): {
  iconLinks: Array<LinkType | ActionType>;
  textLinks: Array<LinkType | ActionType>;
} {
  if (!isMobile) {
    return { textLinks, iconLinks };
  }
  const accountLinks = generateAccountLinks();
  return {
    textLinks: [
      { target: NAV_TARGETS.LEARN, text: ui('links.learn') },
      ...accountLinks,
    ],
    iconLinks: [
      {
        InlineIconComponent: NavLocationIcon,
        label: ui('links.location'),
        target: NAV_TARGETS.LOCATION,
        text: '',
      },
    ],
  };
}
