import type {
  ApplePayPayload,
  GooglePaymentTokenizePayload,
  HostedFieldsTokenizePayload,
  PayPalTokenizePayload,
  VenmoTokenizePayload,
} from 'braintree-web';
import type { ApplePayDetails } from 'braintree-web/apple-pay';
import { PaymentToken } from 'braintree-web/fastlane';
import type { PayPalAccountDetails } from 'braintree-web/paypal';
import { ReactNode, useCallback, useRef, useState } from 'react';

import { Icon } from '~/components/global/Icon/Icon.types';
import { CartKlarnaPayload } from '~/data/models/CartKlarnaResponse';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import { apiGetCartKlarna } from '~/lib/api/checkout/cart-klarna';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

import { useCartSummaryContextSelector } from '../Cart/CartSummary.context';

interface PaymentDataProviderProps {
  children: ReactNode;
}

export interface PaymentDataContextProps {
  clearCrossPageErrorMessage: () => void;
  clearPaymentData: () => void;
  crossPageErrorMessage?: ErrorNotificationContent;
  getKlarnaClientToken: () => void;
  klarnaPayload?: CartKlarnaPayload;
  klarnaSessionToken?: string;
  paymentData: PaymentData;
  paymentMethodCategories?: { identifier: string; name: string }[];
  setCrossPageErrorMessage: (
    crossPageErrorMessage: ErrorNotificationContent | undefined,
  ) => void;
  setPaymentData: (paymentData: PaymentData) => void;
  setSynchronyFourDigit: (synchronyFourDigit: string) => void;
  synchronyFourDigit?: string;
}

export type VenmoPayload = VenmoTokenizePayload & { deviceData: string };

export type BraintreePayload =
  | HostedFieldsTokenizePayload
  | PayPalTokenizePayload
  | GooglePaymentTokenizePayload
  | VenmoPayload
  | ApplePayPayload
  | PaymentToken;

export interface ResolvePayload {
  chargeId: string;
}

export interface SynchronyPayload {
  lastFourDigit: string;
}

export function isResolvePayload(payload: unknown): payload is ResolvePayload {
  return (
    (typeof payload === 'object' && payload && 'chargeId' in payload) ?? false
  );
}

export function isFastlanePayload(payload: unknown): payload is PaymentToken {
  return (
    (typeof payload === 'object' &&
      payload &&
      'id' in payload &&
      'paymentSource' in payload) ??
    false
  );
}

export type PaymentPayload = BraintreePayload | ResolvePayload;

export function isPayPalPayload(
  payload: unknown,
): payload is PayPalTokenizePayload {
  return (
    (typeof payload === 'object' &&
      payload &&
      'nonce' in payload &&
      'details' in payload &&
      'payerId' in (payload.details as PayPalAccountDetails)) ??
    false
  );
}

export function isApplePayPayload(
  payload: unknown,
): payload is ApplePayPayload {
  return (
    (typeof payload === 'object' &&
      payload &&
      'consumed' in payload &&
      'details' in payload &&
      'dpanLastTwo' in (payload.details as ApplePayDetails)) ??
    false
  );
}

export interface PaymentData {
  payload?: PaymentPayload;
  paymentType?: PAYMENT_OPTIONS;
}

const PaymentDataContext = createContext<PaymentDataContextProps>();

const emptyPaymentData = {
  paymentType: undefined,
  payload: undefined,
};

export enum ErrorContentType {
  BILLING_INFO = 'billing_info',
  USER_INFO = 'user_info',
}

export type ErrorNotificationContent = {
  icon?: Icon;
  subtitle?: string;
  title?: string;
  type: ErrorContentType;
};

function useContextSetup(): PaymentDataContextProps {
  const [paymentData, setPaymentData] = useState<PaymentData>(emptyPaymentData);
  const [crossPageErrorMessage, setCrossPageErrorMessage] =
    useState<ErrorNotificationContent>();
  const [synchronyFourDigit, setSynchronyFourDigit] = useState<
    string | undefined
  >();
  const [klarnaSessionToken, setKlarnaSessionToken] = useState<
    string | undefined
  >(undefined);
  const [klarnaPayload, setKlarnaPayload] = useState<
    CartKlarnaPayload | undefined
  >(undefined);
  const isRequestInProgress = useRef(false);
  const [paymentMethodCategories, setPaymentMethodCategories] = useState<
    { identifier: string; name: string }[] | undefined
  >(undefined);

  const cartSummaryId = useCartSummaryContextSelector((v) => v.cartSummary?.id);

  const clearCrossPageErrorMessage = useCallback(() => {
    setCrossPageErrorMessage(undefined);
  }, []);

  const clearPaymentData = useCallback(() => {
    setPaymentData(emptyPaymentData);
  }, []);

  const getKlarnaClientToken = useCallback(async () => {
    if (cartSummaryId && !isRequestInProgress.current) {
      isRequestInProgress.current = true;
      const response = await apiGetCartKlarna(`${cartSummaryId}`);
      if (response.isSuccess) {
        setKlarnaSessionToken(response.data.klarnaSession.client_token);
        setKlarnaPayload(response.data.klarnaPayload);
        setPaymentMethodCategories(
          response.data.klarnaSession.payment_method_categories,
        );
      }
      isRequestInProgress.current = false;
    }
  }, [cartSummaryId]);

  return {
    clearCrossPageErrorMessage,
    clearPaymentData,
    crossPageErrorMessage,
    getKlarnaClientToken,
    klarnaPayload,
    klarnaSessionToken,
    paymentData,
    paymentMethodCategories,
    setCrossPageErrorMessage,
    setPaymentData,
    setSynchronyFourDigit,
    synchronyFourDigit,
  };
}

export function PaymentDataContextProvider({
  children,
}: PaymentDataProviderProps) {
  const value = useContextSetup();

  return (
    <PaymentDataContext.Provider value={value}>
      {children}
    </PaymentDataContext.Provider>
  );
}

export const usePaymentDataContextSelector = <SelectedValue,>(
  selector: Selector<PaymentDataContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<PaymentDataContextProps, SelectedValue>(
    PaymentDataContext,
    selector,
    equalCompareFn,
  );
