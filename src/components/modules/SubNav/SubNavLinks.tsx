import isStrictEqual from 'fast-deep-equal';
import { memo, useCallback } from 'react';

import CloseIcon from '~/assets/icons/key-pages/close.svg';
import Link from '~/components/global/Link/Link';
import NavLink, { LocationNavLink } from '~/components/global/Link/NavLink';
import { buildSubNavLinks } from '~/components/modules/Nav/mappers/links';
import {
  ActionType,
  LinkType,
  NAV_TARGETS,
} from '~/components/modules/Nav/Nav.types';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useNavContextSelector } from '~/context/Nav.context';
import { useUserLocationContextSelector } from '~/context/UserLocation.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import useRouter from '~/hooks/useRouter';
import { THEME } from '~/lib/constants/theme';
import { isSimpleShopDeployment } from '~/lib/utils/deploy';
import { checkSSOTokenInCookie, getSSOLoginURL } from '~/lib/utils/sso';
import { ui } from '~/lib/utils/ui-dictionary';

import { useAccountContextSelector } from '../Account/Account.context';
import styles from './SubNav.styles';

interface SubNavLinksProps {
  isMobile: boolean;
}

const GeneralLink = memo(
  ({
    activeLink,
    isLocation,
    link,
  }: {
    activeLink: string;
    isLocation: boolean;
    link: LinkType | ActionType;
  }) => {
    const createSelectLinkHandler = useNavContextSelector(
      (v) => v.createSelectLinkHandler,
    );
    const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);
    const isLocationDisabled = isOTS || isSimpleShopDeployment();

    return isLocation ? null : (
      <li css={styles.link}>
        {'target' in link && link.target === NAV_TARGETS.LOCATION ? (
          <LocationNavLink
            {...link}
            onClick={
              isLocationDisabled ? undefined : createSelectLinkHandler(link)
            }
            isActive={'target' in link && activeLink === link.target}
          />
        ) : (
          <NavLink
            {...link}
            onClick={createSelectLinkHandler(link)}
            isActive={'target' in link && activeLink === link.target}
          />
        )}
      </li>
    );
  },
  (prevProps, nextProps) => {
    return isStrictEqual(prevProps, nextProps);
  },
);

const SSOLink = memo(
  ({ link }: { link: LinkType }) => {
    const handleLogout = useAccountContextSelector((v) => v.handleLogout);
    const userType = useUserPersonalizationContextSelector((v) => v.userType);
    const router = useRouter();

    // this method is seprately created for mobile version
    // since NavLink is designed to work with navigation within steer
    // this method handles to remove local cookies, redirects to/from SSO etc
    const handleLinkClick = useCallback(
      (linkName: string) => {
        if (linkName === ui('links.logout')) {
          handleLogout(userType);
          return;
        } else if (linkName === ui('links.account')) {
          const ssoTokenInCookie = checkSSOTokenInCookie();
          const redirectURL = getSSOLoginURL();
          if (!ssoTokenInCookie) {
            window.location.href = redirectURL;
          } else {
            router.push(redirectURL);
          }
          return;
        }
      },
      [handleLogout, userType, router],
    );

    return (
      <button
        css={styles.ssoLink}
        onClick={() => handleLinkClick(link.text ? link.text : '')}
      >
        {link.text}
      </button>
    );
  },
  (prevProps, nextProps) => {
    return isStrictEqual(prevProps, nextProps);
  },
);

const CloseLink = memo(({ activeLink }: { activeLink: string }) => {
  const handleCloseSubNav = useNavContextSelector((v) => v.handleCloseSubNav);
  const setBrowserLocationFailed = useUserLocationContextSelector(
    (v) => v.setBrowserLocationFailed,
  );

  const onSubNavClose = useCallback(() => {
    handleCloseSubNav();
    if (setBrowserLocationFailed) {
      setBrowserLocationFailed(false);
    }
  }, [handleCloseSubNav, setBrowserLocationFailed]);

  return (
    <li css={styles.link}>
      <Link
        as="button"
        InlineIconComponent={CloseIcon}
        disableInlineIconComponentCustomContainerStyle
        theme={THEME.LIGHT}
        aria-label={`${ui('nav.close')} ${activeLink}`}
        onClick={onSubNavClose}
        css={styles.closeSubNav}
      />
    </li>
  );
});

function SubNavLinks({ isMobile }: SubNavLinksProps) {
  const activeLink = useNavContextSelector((v) => v.activeLink);
  const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);

  const { textLinks, iconLinks } = buildSubNavLinks({ isMobile });
  const filterTextLinks = isOTS
    ? textLinks.filter(
        (item) =>
          item.text !== 'Learn' &&
          item.text !== 'Deals' &&
          item.text !== 'Track your order',
      )
    : isSimpleShopDeployment()
      ? textLinks.filter((item) => item.text !== 'Learn')
      : textLinks;
  const isLocation = activeLink === 'LOCATION';

  return (
    <ul css={[styles.subnavLinkList, isLocation && styles.locationNav]}>
      <span css={styles.linkSection}>
        {filterTextLinks.map((item, index) =>
          item.text === ui('links.account') ||
          item.text === ui('links.logout') ? (
            <SSOLink key={index} link={item as LinkType} />
          ) : (
            <GeneralLink
              activeLink={activeLink}
              isLocation={isLocation}
              link={item}
              key={index}
            />
          ),
        )}
      </span>
      <span css={[styles.linkSection, styles.linkSectionIcons]}>
        {iconLinks.map((link, index) => (
          <GeneralLink
            activeLink={activeLink}
            isLocation={isLocation}
            link={link}
            key={index}
          />
        ))}
        <CloseLink activeLink={activeLink} />
      </span>
    </ul>
  );
}

export default memo(SubNavLinks);
