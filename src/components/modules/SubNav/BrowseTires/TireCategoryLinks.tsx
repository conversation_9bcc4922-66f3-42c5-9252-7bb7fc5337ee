import isStrictEqual from 'fast-deep-equal';
import { memo } from 'react';

import GridItem from '~/components/global/Grid/GridItem';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Link from '~/components/global/Link/Link';
import { dealsLink } from '~/components/modules/Nav/mappers/links';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useNavContextSelector } from '~/context/Nav.context';
import { useSiteMenuContextSelector } from '~/context/SiteMenu.context';
import { useWidgetConfigContextSelector } from '~/context/WidgetConfig.context';
import { THEME } from '~/lib/constants/theme';
import { isSimpleShopDeployment } from '~/lib/utils/deploy';
import { ui } from '~/lib/utils/ui-dictionary';

import { NAV_TARGETS } from '../../Nav/Nav.types';
import { useSearchContextSelector } from '../../Search/Search.context';
import { useSearchModalContextSelector } from '../../Search/SearchModal.context';
import SelectedInstaller from '../../SelectedInstaller/SelectedInstaller';
import styles from './BrowseTires.styles';

function CategoryLink({
  onClick,
  title,
}: {
  onClick: () => void;
  title: string;
}) {
  const activeCategory = useNavContextSelector((v) => v.activeCategory);
  const isSelected = activeCategory === title;

  return (
    <span key={title} css={[styles.container, isSelected && styles.selected]}>
      <div css={isSelected && styles.decoration} />
      <button
        aria-label={title}
        aria-current={isSelected}
        css={styles.label}
        onClick={onClick}
      >
        {title}
      </button>
    </span>
  );
}

function SiteMenuLinks() {
  const siteMenuBrowseList = useSiteMenuContextSelector(
    (v) => v.siteMenuBrowseList,
  );
  const createSelectCategoryHandler = useNavContextSelector(
    (v) => v.createSelectCategoryHandler,
  );

  return siteMenuBrowseList.map(({ title }, index) => (
    <CategoryLink
      title={title}
      onClick={createSelectCategoryHandler(title)}
      key={index}
    />
  ));
}

function VehicleAndSizeLinks() {
  const { lockSearchStateToTireSize, lockSearchStateToVehicle } =
    useSearchContextSelector((v) => ({
      lockSearchStateToTireSize: v.lockSearchStateToTireSize,
      lockSearchStateToVehicle: v.lockSearchStateToVehicle,
    }));
  const { setIsSearchOpen, setCurrentInputQuery } =
    useSearchModalContextSelector((v) => ({
      setIsSearchOpen: v.setIsSearchOpen,
      setCurrentInputQuery: v.setCurrentInputQuery,
    }));
  const make = useWidgetConfigContextSelector((v) => v.widgetAppConfig?.make);

  const onVehicleCTAClick = () => {
    lockSearchStateToVehicle(make ? make : undefined); // if it is OTS with make available lock the search state to the make
    setCurrentInputQuery({
      queryText: make ? make : '', // if it is OTS with make available lock the search state to the make
    });
    setIsSearchOpen(true);
  };

  const onTireSizeCTAClick = () => {
    lockSearchStateToTireSize();
    setIsSearchOpen(true);
  };

  return (
    <div>
      <CategoryLink title={ui('nav.vehicle')} onClick={onVehicleCTAClick} />
      <CategoryLink title={ui('nav.size')} onClick={onTireSizeCTAClick} />
    </div>
  );
}

function TireCategoryLinks({ isMobile }: { isMobile: boolean }) {
  const { isOTS } = useGlobalsContextSelector((v) => ({
    isOTS: Number(v.isOTS) === 1,
  }));
  return (
    <GridItem gridColumnM="1/3" gridColumnL="1/4">
      <div css={styles.installerContainer}>
        <SelectedInstaller
          showLocation={false}
          theme={THEME.DARK}
          target={NAV_TARGETS.LOCATION}
          yourShopHeading
        />
      </div>
      <div css={styles.header}>
        {isMobile
          ? ui('nav.browseTires.mobileHeader')
          : ui('nav.browseTires.header')}
      </div>
      {!isOTS && !isMobile && !isSimpleShopDeployment() && <SiteMenuLinks />}
      <VehicleAndSizeLinks />
      {!isOTS && isMobile && (
        <span css={styles.container}>
          <Link
            css={[styles.label, styles.dealsLink]}
            icon={ICONS.FIRE}
            href={dealsLink.href}
            isExternal={dealsLink.isExternal}
          >
            {dealsLink.text}
          </Link>
        </span>
      )}
    </GridItem>
  );
}

export default memo(TireCategoryLinks, (prevProps, nextProps) =>
  isStrictEqual(prevProps, nextProps),
);
