import GridItem from '~/components/global/Grid/GridItem';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useSiteMenuContextSelector } from '~/context/SiteMenu.context';
import { isSimpleShopDeployment } from '~/lib/utils/deploy';

import styles from './BrowseTires.styles';
import Categories from './Categories';
import TireCategoryLinks from './TireCategoryLinks';

interface Props {
  isMobile: boolean;
  isOpen: boolean;
  isSubNavOpen: boolean;
}

function BrowseTires({ isOpen, isMobile, isSubNavOpen }: Props) {
  const siteMenuBrowseList = useSiteMenuContextSelector(
    (v) => v.siteMenuBrowseList,
  );
  const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);

  return isOpen || (isMobile && isSubNavOpen) ? (
    <GridItem gridColumnM="1/8" gridColumnL="1/9" gridColumnXL="1/7" isGrid>
      <GridItem
        gridColumnM="1/4"
        css={[!isOpen && styles.hide, styles.mainLinks]}
      >
        {!isMobile && <TireCategoryLinks isMobile={isMobile} />}
      </GridItem>
      {!isOTS &&
        !isSimpleShopDeployment() &&
        siteMenuBrowseList.map(
          ({ info, siteMenuBrowseGroupList, title }, idx: number) => (
            <Categories
              key={idx}
              {...{
                category: title,
                info,
                isMobile,
                isOpen,
                siteMenuBrowseGroupList,
              }}
              isSubNavOpen={isSubNavOpen}
            />
          ),
        )}
    </GridItem>
  ) : null;
}

export default BrowseTires;
