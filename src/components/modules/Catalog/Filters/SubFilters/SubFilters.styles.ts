import { BORDERS } from '~/lib/constants/borders';
import { MQ } from '~/lib/constants/breakpoints';
import { COLORS } from '~/lib/constants/colors';
import { SPACING } from '~/lib/constants/spacing';
import { StylesMap } from '~/lib/constants/styles.types';
import { Z_INDEX } from '~/lib/constants/zindex';
import { typography } from '~/styles/typography.styles';

const TOPS = {
  S: 70,
  M: 99,
  L: 119,
};

const styles: StylesMap = {
  decorator: {
    ':after': {
      content: '"•"',
      fontSize: 6,
      padding: SPACING.SIZE_10,
    },
    [MQ.L]: {
      ':after': {
        content: '""',
        padding: 'unset',
      },
    },
  },
  disableEvents: {
    pointerEvents: 'none',
  },
  label: [
    typography.labelHeadline,
    {
      fontWeight: 'normal',
      color: COLORS.LIGHT.GRAY_70,
      marginRight: SPACING.SIZE_15,
    },
  ],
  range: [
    typography.labelCopyTight,
    {
      flexGrow: 1,
    },
  ],
  rangeLabel: [
    typography.labelCopyTight,
    {
      color: COLORS.LIGHT.GRAY_70,
      marginRight: SPACING.SIZE_05,
    },
  ],
  rangePrefix: [
    typography.labelHeadline,
    {
      marginRight: SPACING.SIZE_05,
      [MQ.L]: {
        display: 'none',
      },
    },
  ],
  rangePrefixHide: {
    display: 'none',
  },
  root: {
    alignItems: 'baseline',
    background: COLORS.GLOBAL.WHITE,
    position: 'sticky',
    top: TOPS.S,
    zIndex: Z_INDEX.FRONT - 1,
    [MQ.M]: {
      top: TOPS.M,
    },
    [MQ.L]: {
      top: TOPS.L,
    },
  },
  slider: {
    width: 200,
  },
  smallHide: {
    [MQ.S]: {
      display: 'none',
    },
    [MQ.L]: {
      display: 'initial',
    },
  },
  smallShow: {
    [MQ.L]: {
      display: 'none',
    },
  },
  sort: typography.labelCopy,
  sortBy: {
    alignItems: 'center',
    display: 'flex',
    marginLeft: SPACING.SIZE_20,
    [MQ.M]: {
      marginLeft: SPACING.SIZE_40,
    },
  },
  sortLabel: [
    typography.labelCopy,
    {
      color: COLORS.LIGHT.GRAY_70,
      marginRight: SPACING.SIZE_05,
    },
  ],
  subFilterContainer: {
    '& > span': {
      borderBottom: '3px solid transparent',
      marginRight: SPACING.SIZE_20,
      paddingBottom: SPACING.SIZE_20,
      paddingTop: SPACING.SIZE_20,
      whiteSpace: 'nowrap',
      [MQ.L]: {
        marginRight: SPACING.SIZE_50,
      },
    },
    '.active': {
      button: {
        ':hover, :hover:not(:active)': {
          color: COLORS.GLOBAL.BLACK,
        },
        '&:focus:not(:active)': {
          color: COLORS.GLOBAL.BLACK,
        },
        fontWeight: 'bold',
        color: COLORS.GLOBAL.BLACK,
      },
      borderBottom: '3px solid #181818',
    },
    alignItems: 'center',
    button: {
      ':hover, :hover:not(:active)': {
        color: COLORS.LIGHT.GRAY_70,
      },
      color: COLORS.LIGHT.GRAY_70,
    },
    span: {
      button: {
        '&:active, &:hover:not(:active), &:focus:not(:active)': {
          color: COLORS.LIGHT.GRAY_70,
        },
      },
    },
  },
  toggle: {
    alignItems: 'center',
    display: 'flex',
  },
  wrapper: {
    borderBottom: BORDERS.SOLID_GRAY_20_1PX,
    minHeight: 48,
  },
};

export default styles;
