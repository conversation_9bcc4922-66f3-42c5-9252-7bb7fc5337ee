import type { ApplePayPayload, PayPalTokenizePayload } from 'braintree-web';
import dynamic from 'next/dynamic';
import { ReactNode, useState } from 'react';

import { BillingInfoValues } from '~/components/pages/CheckoutPage/Payment/BillingInfoForm/BillingInfo.types';
import { PaymentErrorType } from '~/components/pages/CheckoutPage/Payment/PaymentContext/Payment.context';
import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import { SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

import { useCartUserActionContextSelector } from '../../CartUserAction.context';

const PlaceorderEffectWithBraintree = dynamic(
  () => import('./PlaceorderEffectWithBraintree'),
);

interface CartModalPayContextProps {
  billingInfo: Partial<BillingInfoValues>;
  cartShippingInfo?: SiteCartShippingRequest;
  isLoading: boolean;
  isSaveBillingSuccess: boolean;
  paymentError?: PaymentErrorType;
  paymentPayload?: ApplePayPayload | PayPalTokenizePayload;
  setBillingInfo: (value: Partial<BillingInfoValues>) => void;
  setCartShippingInfo: (values: SiteCartShippingRequest) => void;
  setIsLoading: (value: boolean) => void;
  setIsSaveBillingSuccess: (value: boolean) => void;
  setPaymentError: (error: PaymentErrorType) => void;
  setPaymentPayload: (
    payload: ApplePayPayload | PayPalTokenizePayload | undefined,
  ) => void;
}

interface CartModalPayProviderProps {
  children: ReactNode;
  initialBillingInfo: Partial<BillingInfoValues>;
  siteShipping?: SiteCartShippingResponse | null;
}

const CartModalPayContext = createContext<CartModalPayContextProps>();

function useContextSetup({
  initialBillingInfo,
}: CartModalPayProviderProps): CartModalPayContextProps {
  const [billingInfo, setBillingInfo] =
    useState<Partial<BillingInfoValues>>(initialBillingInfo);

  useCartUserActionContextSelector((v) => ({
    activeOption: v.activeOption,
    isShipToMeTabSelected: v.isShipToMeTabSelected,
  }));

  const [paymentError, setPaymentError] = useState<PaymentErrorType>();
  const [paymentPayload, setPaymentPayload] = useState<
    ApplePayPayload | PayPalTokenizePayload
  >();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaveBillingSuccess, setIsSaveBillingSuccess] = useState(false);
  const [cartShippingInfo, setCartShippingInfo] =
    useState<SiteCartShippingRequest>();

  return {
    billingInfo,
    cartShippingInfo,
    isLoading,
    isSaveBillingSuccess,
    paymentError,
    paymentPayload,
    setBillingInfo,
    setCartShippingInfo,
    setIsLoading,
    setIsSaveBillingSuccess,
    setPaymentError,
    setPaymentPayload,
  };
}

export function CartModalPayContextProvider({
  children,
  initialBillingInfo,
  siteShipping,
}: CartModalPayProviderProps) {
  const value = useContextSetup({
    initialBillingInfo,
    children,
  });

  return (
    <CartModalPayContext.Provider value={value}>
      <PlaceorderEffectWithBraintree
        initialBillingInfo={initialBillingInfo}
        siteShipping={siteShipping}
      />
      {children}
    </CartModalPayContext.Provider>
  );
}

export const useCartModalPayContextSelector = <SelectedValue,>(
  selector: Selector<CartModalPayContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<CartModalPayContextProps, SelectedValue>(
    CartModalPayContext,
    selector,
    equalCompareFn,
  );
