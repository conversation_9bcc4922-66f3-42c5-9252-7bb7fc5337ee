import lscache from 'lscache';
import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import AffirmPromoBar from '~/components/global/AffirmPromoBar/AffirmPromoBar';
import {
  AffirmPageType,
  AffirmSteerSource,
} from '~/components/global/AffirmPromoBar/AffirmPromoBar.types';
import Button from '~/components/global/Button/Button';
import ContinueShopping from '~/components/global/ContinueShopping/ContinueShopping';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import { InstallationShopParams } from '~/components/global/InstallationShopDetails/InstallationShopDetailsModal.context';
import BaseLink from '~/components/global/Link/BaseLink';
import Markdown from '~/components/global/Markdown/Markdown';
import BottomCardModal from '~/components/global/Modal/BottomCardModal';
import NotificationContainer from '~/components/global/NotificationBanner/NotificationCarousal';
import PayLaterBannerContainer from '~/components/global/PayLaterBanner/PayLaterBannerContainer';
import { useElement } from '~/components/global/SyncScroll/useElement';
import { ShippingLocationProps } from '~/components/pages/CheckoutPage/CartSummaryPanel/ShippingLocation/ShippingLocation.types';
import {
  PANEL,
  SHIPPING_OPTIONS,
} from '~/components/pages/CheckoutPage/checkout.constants';
import { getDealDetails } from '~/components/pages/CheckoutPage/checkout.util';
import Feedback from '~/components/pages/CheckoutPage/Feedback/Feedback';
import Footer from '~/components/pages/CheckoutPage/Footer/Footer';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { useWidgetConfigContextSelector } from '~/context/WidgetConfig.context';
import { SiteCartAppointmentResponse } from '~/data/models/SiteCartAppointmentResponse';
import { SiteCartCouponItem } from '~/data/models/SiteCartCouponItem';
import {
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import {
  IDMEVerifiedStatus,
  SiteCartSummaryRequest,
} from '~/data/models/SiteCartSummaryRequest';
import { SiteProductSubType } from '~/data/models/SiteProductLineSizeDetail';
import useBreakpoints from '~/hooks/useBreakpoints';
import useRouter from '~/hooks/useRouter';
import useWidgetSource from '~/hooks/useWigetSource';
import { apiUpdateSiteCartSummarySales } from '~/lib/api/v2/sales/sales-cart-summary';
import { ICON_IMAGE_TYPE } from '~/lib/backend/icon-image.types';
import { LOCAL_STORAGE } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { SIMPLE_CREW_SALES_USER_UUID, USER_TYPE } from '~/lib/constants/sso';
import { THEME } from '~/lib/constants/theme';
import { TIME } from '~/lib/constants/time';
import { Z_INDEX } from '~/lib/constants/zindex';
import GA from '~/lib/helpers/analytics';
import { isClient } from '~/lib/helpers/browser';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { mapToCartObject } from '~/lib/helpers/rudderstack/transformer';
import { scrollToRef } from '~/lib/helpers/scroll';
import {
  checkAafesCheckoutUrlinCookie,
  checkAafesCIDinCookie,
  generateCheckoutXML,
} from '~/lib/utils/aafes/aafes-auth';
import { aafesCheckoutParams } from '~/lib/utils/aafes/aafes.constants';
import { seStorage } from '~/lib/utils/browser-storage';
import { isCheckoutPagePath } from '~/lib/utils/routes';
import { formatDollars } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';
import { isSimpleSalesToolUserType } from '~/lib/utils/user-type';
import { getVWOValue } from '~/lib/utils/vwo/getVWOValue';

import LocationModal from '../../Location/LocationModal/LocationModal';
import PurchaseIncludesValueProp from '../../PDP/PurchaseIncludes/PurchaseIncludesValueProp';
import { useCartShippingContextSelector } from '../CartShipping.context';
import { useCartSummaryContextSelector } from '../CartSummary.context';
import { useCartUserActionContextSelector } from '../CartUserAction.context';
import Breakdown from './Breakdown/Breakdown';
import { calculateTotalNumberOfTiresInCart } from './CarSummaryModal.utils';
import { useCartModalPayContextSelector } from './CartModalPayContext/CartModalPay.context';
import CartSummary from './CartSummary/CartSummary';
import styles, {
  stickCtaStyles,
  stickyStyles,
} from './CartSummaryModal.styles';
import DealsAndRebatsInCartContainer from './DealsAndRebatsInCart/DealsAndRebatsInCart.container';
import { PromoListItem } from './DealsAndRebatsInCart/PromoCard.types';
import NotificationList from './NotificationList/NotificationList';
import ProductInCart from './ProductInCart/ProductInCart';
import ShippingServiceContainer from './ShippingService/ShippingService.container';
import { InstallErrorStateFromType } from './ShippingService/ShippingService.types';
import { isValidCartShipping } from './ShippingService/shippingService.utils';

const Loading = dynamic(() => import('~/components/global/Loading/Loading'));
const Notification = dynamic(
  () => import('~/components/global/NotificationBanner/Notification'),
);

const ProductInCartSkeleton = dynamic(
  () => import('../Loader/ProductInCartLoader/ProductInCartLoader'),
);

const QuickCheckoutWithBraintree = dynamic(
  () => import('./QuickCheckoutWithBraintree/QuickCheckoutWithBraintree'),
);

interface CartSummaryModalProps {
  applyPromoCoupons?: (couponCode: string) => void;
  cartAppointment?: SiteCartAppointmentResponse | null;
  cartShipping?: SiteCartShippingResponse;
  data: SiteCartSummary;
  displayRoadsideAssistance: boolean;
  displayShippingInfo: boolean;
  isCheckoutPage: boolean;
  isLoading: boolean;
  isOpen: boolean;
  isRoadHazardCheckboxVisible: boolean;
  isRoadHazardChecked: boolean;
  isShowingRemoveInstallationAlert: boolean;
  isUpdatingRoadHazard: boolean;
  onApplyPromoCode: (couponCode: string) => void;
  onCheckChange: (value?: boolean) => void;
  onCheckout: () => void;
  onClose: () => void;
  onRemoveInstaller: () => void;
  onRemoveProduct: (productId: number) => void;
  onRemovePromoCode: (couponCode: string, coupon?: SiteCartCouponItem) => void;
  onUpdateQuantity: ({
    productId,
    quantity,
  }: {
    productId: number;
    quantity: number;
  }) => Promise<void>;
  onUpdateVerifyStatus: (status: IDMEVerifiedStatus) => void;
  openInstallationDetailsModal: (value: boolean) => void;
  removeOnlyInstaller: () => void;
  setCurrentProductId: (productId?: number) => void;
  setInstallationParams: (value: InstallationShopParams) => void;
  setIsAllShopModalOpen: (isOpen: boolean) => void;
  setIsAppointmentModalOpen: (isOpen: boolean) => void;
  setIsCartSummaryModalOpen: (value: boolean) => void;
  setIsShowingRemoveInstallationAlert: (value: boolean) => void;
  userType?: USER_TYPE;
}

export const TIRE_LOADER_URL =
  'https://images.simpletire.com/image/upload/v1644508305/steer/common/tire-white.gif';

function CartSummaryModal({
  data,
  displayRoadsideAssistance,
  onCheckout,
  isCheckoutPage,
  isOpen,
  isShowingRemoveInstallationAlert,
  onClose,
  onRemoveProduct,
  onRemovePromoCode,
  onUpdateQuantity,
  onUpdateVerifyStatus,
  onApplyPromoCode,
  cartAppointment,
  cartShipping,
  userType,
  removeOnlyInstaller,
  setIsShowingRemoveInstallationAlert,
  isRoadHazardChecked,
}: CartSummaryModalProps) {
  const {
    siteCartCoupons,
    idMeVerifiedStatus,
    installerDetails,
    siteProducts,
    eligibleManualPromoCoupons,
    idMeDiscountInCents,
    estimatedFetTaxInCents,
    estimatedStateFeesInCents,
    estimatedTaxInCents,
    estimatedProfitInCents,
    shippingCostInCents,
    isCouponReplacementMessage,
    couponReplacementCoupons,
    isPriceChanged,
    delayDays,
    delayAlert,
    roadHazardCostInCents,
    savingTodayInCents,
    totalInCents,
    subTotalInCents,
    hasRoadsideAssistance,
    idMeEligibleDiscountsInCents,
  } = data;

  const {
    cartSummary,
    hasMobileInstall,
    isLoading,
    setIsCartSummaryModalOpen,
    trcOption,
  } = useCartSummaryContextSelector((v) => ({
    cartSummary: v.cartSummary,
    hasMobileInstall: v.hasMobileInstall,
    isLoading: v.isLoading,
    setIsCartSummaryModalOpen: v.setIsCartSummaryModalOpen,
    trcOption: v.trcOption,
  }));

  const otsName = useWidgetConfigContextSelector(
    (v) => v.widgetAppConfig?.name || '',
  );
  const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);

  const vwoExperimentId4 = useGlobalsContextSelector((v) => v.vwoExperimentId4);
  const experimentId4 = getVWOValue(vwoExperimentId4);
  const {
    isAddingToCart,
    isApiLoading,
    isDisplayErrorNotification,
    setIsShippingFormOpenFromCheckoutBtn,
    currentTab,
    currentTabType,
    activeOption,
    setIsDisplayErrorNotification,
    setIsShippingFormOpen,
    setIsShopCardError,
    setIsFedexShopCardError,
    setActiveOption,
    isShipToMeTabSelected,
    setPaypalShipToHomeSelected,
    isFedexTabSelected,
    isWarehouseTabSelected,
  } = useCartUserActionContextSelector((v) => ({
    activeOption: v.activeOption,
    currentTab: v.currentTab,
    currentTabType: v.currentTabType,
    isAddingToCart: v.isAddingToCart,
    isApiLoading: v.isApiLoading,
    isDisplayErrorNotification: v.isDisplayErrorNotification,
    isFedexTabSelected: v.isFedexTabSelected,
    isShipToMeTabSelected: v.isShipToMeTabSelected,
    isWarehouseTabSelected: v.isWarehouseTabSelected,
    setActiveOption: v.setActiveOption,
    setIsDisplayErrorNotification: v.setIsDisplayErrorNotification,
    setIsFedexShopCardError: v.setIsFedexShopCardError,
    setIsShippingFormOpen: v.setIsShippingFormOpen,
    setIsShippingFormOpenFromCheckoutBtn:
      v.setIsShippingFormOpenFromCheckoutBtn,
    setIsShopCardError: v.setIsShopCardError,
    setPaypalShipToHomeSelected: v.setPaypalShipToHomeSelected,
  }));
  const {
    isShopLoading,
    isCartShippingDataLoading,
    isSHCostFree,
    isSHCostTBD,
    pickupLocations,
    prevCartProducts,
    selectedLocation,
    warehouseSelectedLocation,
  } = useCartShippingContextSelector((v) => ({
    getInstallerSchedule: v.getInstallerSchedule,
    isCartShippingDataLoading: v.isLoading,
    isLoadingInstallerSchedule: v.isLoadingInstallerSchedule,
    isSHCostFree: v.isSHCostFree,
    isSHCostTBD: v.isSHCostTBD,
    isShopLoading: v.isShopLoading,
    pickupLocations: v.pickupLocations,
    prevCartProducts: v.prevCartProducts,
    selectedLocation: v.selectedLocation,
    warehouseSelectedLocation: v.warehouseSelectedLocation,
  }));
  const isSimpleShop = useGlobalsContextSelector(
    (v) => Number(v.isSimpleShop) === 1,
  );
  const {
    deliveryMethods,
    isDealerTire,
    stateAbbr,
    isOccupationDiscountsAvailable,
    isTireReplacementCoverageAvailable,
  } = useUserPersonalizationContextSelector((v) => ({
    deliveryMethods: v.deliveryMethods,
    isDealerTire: v.isDealerTire,
    isOccupationDiscountsAvailable: v.isOccupationDiscountsAvailable,
    isRetailUser: v.isRetail,
    isTireReplacementCoverageAvailable: v.isTireReplacementCoverageAvailable,
    stateAbbr: v.stateAbbr,
  }));
  const isPayLoading = useCartModalPayContextSelector((v) => v.isLoading);

  const { is } = useBreakpoints();
  const router = useRouter();
  const isCartInstallable = cartSummary?.siteProducts?.every(
    (product) => product.isInstallable,
  );
  const isCartUnInstallable = cartSummary?.siteProducts?.every(
    (product) => !product.isInstallable,
  );

  const [totalSalePriceAfterSimpleCrew, setTotalSalePriceAfterSimpleCrew] =
    useState(0);

  const [totalSalePriceBeforeSimpleCrew, setTotalSalePriceBeforeSimpleCrew] =
    useState(0);
  const [showShopRemovedAlert, setShowShopRemovedAlert] = useState(false);

  const [showTaxExemptNotification, setShowTaxExemptNotification] = useState(
    cartSummary?.showTaxExemptNotification,
  );

  useEffect(() => {
    setShowShopRemovedAlert(!isCartInstallable && !isCartUnInstallable);
  }, [isCartInstallable, isCartUnInstallable]);

  const state = cartShipping?.cartShipping?.state ?? stateAbbr;
  const nonContinentalStates = ['AK', 'HI', 'PR'];
  const isNonContinental = nonContinentalStates.includes(state);
  const { isSourcePirelliWidget } = useWidgetSource();
  // additional layer of variable saving
  // so that the data from put request does not overlap the initial get request
  // also need to clear the notification on modal close.
  const [priceChangeAlert, setIsPriceChanged] = useState(isPriceChanged);
  const [rootElement, root] = useElement();
  const [targetElement, target] = useElement();
  const serviceContainerRef = useRef(null);
  const modalContentRef = useRef<HTMLDivElement>(undefined);
  const [stickyTheme, setStickyTheme] = useState<THEME>(THEME.ORANGE);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [isApplePayEnabled, setIsApplePayEnabled] = useState(true);
  const [isInstallErrorStateModalOpen, setInstallErrorStateModalOpen] =
    useState(false);
  const [
    showInstallationUnavailableAlert,
    setShowInstallationUnavailableAlert,
  ] = useState<boolean>(false);
  const [installErrorStateTrigger, setInstallErrorStateTrigger] =
    useState<InstallErrorStateFromType>(InstallErrorStateFromType.CHECKOUT);

  const hasFreight = siteProducts.some(
    (product) => product.isShippingViaFreight,
  );

  const isWHolesaleUserHasAddressDefault =
    userType &&
    shippingCostInCents !== 0 &&
    userType !== USER_TYPE.RETAIL &&
    userType !== USER_TYPE.SPECIAL_ORDER;
  const toggleLocationModal = useCallback(() => {
    setIsLocationModalOpen(
      (currentIsLocationModalOpen) => !currentIsLocationModalOpen,
    );
  }, []);

  const hasUninstallable = siteProducts.some(
    (product) => !product.isInstallable,
  );

  const cartInstallable = siteProducts.every(
    (product) => product.isInstallable,
  );

  const containsTrailerTires = useMemo(
    () =>
      siteProducts.some(
        (item) => item.productSubType === SiteProductSubType.TRAILER,
      ),
    [siteProducts],
  );

  useEffect(() => {
    const isSimpleSalesToolUser = isSimpleSalesToolUserType();
    if (cartSummary && isSimpleSalesToolUser && cartSummary.cartUuid) {
      (async () => {
        const query = {
          id: cartSummary.cartUuid,
        };
        const response = await apiUpdateSiteCartSummarySales({
          includeUserRegion: true,
          includeUserSSOUid: false,
          includeUserZip: true,
          query,
        });
        if (response.isSuccess) {
          setTotalSalePriceBeforeSimpleCrew(
            response?.data.siteCart.totalInCents,
          );
        }
      })();
    }
  }, [cartSummary]);

  useEffect(() => {
    const isSimpleSalesToolUser = isSimpleSalesToolUserType();
    if (cartSummary && isSimpleSalesToolUser && cartSummary.cartUuid) {
      (async () => {
        const query = {
          id: cartSummary?.cartUuid,
        };
        const requestBody: SiteCartSummaryRequest = {
          fromSimpleSalesTool: true,
          ssoUid: SIMPLE_CREW_SALES_USER_UUID,
        };
        const response = await apiUpdateSiteCartSummarySales({
          includeUserRegion: true,
          includeUserSSOUid: false,
          includeUserZip: true,
          input: requestBody,
          query,
        });
        if (response.isSuccess) {
          setTotalSalePriceAfterSimpleCrew(
            response?.data.siteCart.totalInCents,
          );
        }
      })();
    }
  }, [cartSummary]);

  useEffect(() => {
    if (isPriceChanged && !priceChangeAlert) {
      setIsPriceChanged(isPriceChanged);
    }
  }, [isPriceChanged, priceChangeAlert]);

  const handleClose = () => {
    setIsPriceChanged(false);
    onClose();
  };

  const handleRemoveItemInCart = async (productId: number) => {
    if (siteProducts.length === 1) {
      setActiveOption(ShippingType.HOME);
    }
    await onRemoveProduct(productId);
  };

  useEffect(() => {
    if (hasUninstallable && installerDetails) {
      setShowInstallationUnavailableAlert(true);
      removeOnlyInstaller();
    }
  }, [hasUninstallable, removeOnlyInstaller, installerDetails]);

  useEffect(() => {
    if (siteProducts.length) {
      return;
    }
    // SEO-489: partner program user may add a product which isn't an associated brand to cart. when the product is removed automatically on sercide API side, we still need to keep CartSummaryModal open to show notification.
    if (siteProducts.length === 0 && cartSummary?.brandNotAssociatedAlert) {
      return;
    }

    //after success of API call should close the modal automatically
    onClose();
  }, [siteProducts.length, cartSummary?.brandNotAssociatedAlert, onClose]);

  const hasOversize = siteProducts.some((product) => product.isOversizeTire);

  const toggleInstallErrorStateModal = () => {
    setInstallErrorStateModalOpen((prev) => !prev);
  };

  const aafesCid = checkAafesCIDinCookie();
  const aafesCheckoutURL = checkAafesCheckoutUrlinCookie();

  const onCloseUnavailableAlert = () => {
    setShowInstallationUnavailableAlert(false);
  };

  const onCloseRemoveInstallationAlert = () => {
    setIsShowingRemoveInstallationAlert(false);
  };

  const pathname = usePathname();
  const isPaymentsPage = isCheckoutPagePath(pathname);

  const handleCheckout = useCallback(() => {
    setPaypalShipToHomeSelected(false);
    if (isCheckoutPage && is.XL) {
      onClose();
      return;
    }
    const isSimpleSalesToolUser = isSimpleSalesToolUserType();

    //STHD-9011: If Ship to me selected and  cartshipping is valid, close the modal on payment page
    if (
      !is.XL &&
      isPaymentsPage &&
      currentTabType === SHIPPING_OPTIONS.HOME &&
      cartShipping &&
      isValidCartShipping(cartShipping.cartShipping) &&
      !isSimpleSalesToolUser
    ) {
      onClose();
      return;
    }

    // If paypal button was clicked earlier before checkout button, clear its selection
    if (lscache.get(LOCAL_STORAGE.ISPAYPAL_SELECTED)) {
      lscache.set(LOCAL_STORAGE.ISPAYPAL_SELECTED, false);
    }

    if (isDealerTire && currentTab === PANEL.MID) {
      setIsShippingFormOpen(true);
      setIsShippingFormOpenFromCheckoutBtn(false);
      setIsShippingFormOpenFromCheckoutBtn(true);
      return;
    }

    // if current tab is Ship to shop or Mobile install and appointment is not confirmed
    // appointment is not matching the tab selected
    if (
      !isDealerTire &&
      cartInstallable &&
      (currentTab === PANEL.LEFT ||
        (hasMobileInstall && currentTab === PANEL.MID)) &&
      (!cartShipping ||
        !cartAppointment ||
        (!cartAppointment?.installer?.isMobileInstall &&
          currentTab === PANEL.MID) ||
        (cartAppointment?.installer?.isMobileInstall &&
          currentTab === PANEL.LEFT))
    ) {
      setIsShopCardError(true);
      setInstallErrorStateModalOpen(true);
      setInstallErrorStateTrigger(InstallErrorStateFromType.CHECKOUT);
      setIsDisplayErrorNotification(true);
      scrollToRef(
        serviceContainerRef,
        TIME.MS1000,
        undefined,
        modalContentRef.current,
        240,
      );
      return;
    }

    if (isFedexTabSelected && !selectedLocation) {
      setIsFedexShopCardError(true);
      return;
    }

    if (isWarehouseTabSelected && !warehouseSelectedLocation) {
      setIsDisplayErrorNotification(true);
      return;
    }

    if (currentTabType === SHIPPING_OPTIONS.HOME && !isSimpleSalesToolUser) {
      GA.addToDataLayer({
        cartSummary,
        event: 'isCheckoutStep',
        shipToOption: 'ShipToMe',
        shipToRefId: '',
        stepName: 'ChooseShipping ',
      });

      if (
        cartShipping &&
        isValidCartShipping(cartShipping.cartShipping) &&
        isSourcePirelliWidget
      ) {
        setIsCartSummaryModalOpen(false);
      } else {
        setIsShippingFormOpen(true);
        setIsShippingFormOpenFromCheckoutBtn(false);
        setIsShippingFormOpenFromCheckoutBtn(true);
      }
      return;
    }
    if (isSimpleSalesToolUser) {
      if (!isCheckoutPage) {
        setIsCartSummaryModalOpen(false);
        setIsShippingFormOpen(false);
        router.push(
          `${ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]}?redirectFrom=cart_modal`,
        );
      }
      return;
    }

    if (!aafesCheckoutURL && !aafesCid) {
      setIsFedexShopCardError(false);
      setIsShippingFormOpenFromCheckoutBtn(true);
      onCheckout();
      GA.addToDataLayer({
        event: 'checkout',
        ecommerce: {
          checkout: {
            actionField: {
              step: 1,
              option: 'StartCheckout:' + cartShipping?.shippingOption,
            },
            products: siteProducts,
          },
        },
      });
      rudderstackSendTrackEvent(RudderstackTrackEventName.CHECKOUT_STARTED, {
        coupon: cartSummary?.siteCartCoupons
          ? { coupon: cartSummary.siteCartCoupons }
          : '',
        currency: 'USD',
        discount: cartSummary?.siteCartCoupons
          ? { discount: cartSummary.siteCartCoupons }
          : '',
        order_id: cartSummary?.cartUuid ?? '',
        products: cartSummary ? mapToCartObject(cartSummary) : '',
        revenue: cartSummary?.totalInCents || 0,
        shipping: cartSummary?.shippingCostInCents ?? '',
        tax: cartSummary?.estimatedTaxInCents ?? '',
      });
      // Do not change anything in the following two lines
      window.VWO = window.VWO || [];
      window.VWO.event =
        window.VWO.event ||
        function () {
          // eslint-disable-next-line prefer-rest-params
          window.VWO.push(['event'].concat([].slice.call(arguments)));
        };

      // Replace the property values with your actual values
      window.VWO.event('rudder.CHECKOUT_STARTED', {
        checkout_id: cartSummary?.cartUuid ?? '',
        coupon: cartSummary?.siteCartCoupons
          ? { coupon: cartSummary.siteCartCoupons }
          : '',
        currency: 'USD',
        discount: cartSummary?.siteCartCoupons
          ? { discount: cartSummary.siteCartCoupons }
          : '',
        installer_id: cartShipping ? cartShipping?.cartShipping.id : '',
        products: cartSummary ? mapToCartObject(cartSummary) : '',
        revenue: cartSummary?.totalInCents || 0,
        shipping_method: cartShipping?.shippingOption,
        step: 1,
        step_name: 'ChooseShipping ',
      });
    }
  }, [
    setPaypalShipToHomeSelected,
    isCheckoutPage,
    isPaymentsPage,
    is.XL,
    isDealerTire,
    currentTab,
    cartInstallable,
    hasMobileInstall,
    cartShipping,
    cartAppointment,
    cartSummary,
    isFedexTabSelected,
    selectedLocation,
    isWarehouseTabSelected,
    warehouseSelectedLocation,
    currentTabType,
    aafesCheckoutURL,
    aafesCid,
    onClose,
    setIsShippingFormOpen,
    setIsShippingFormOpenFromCheckoutBtn,
    setIsShopCardError,
    setIsDisplayErrorNotification,
    setIsFedexShopCardError,
    isSourcePirelliWidget,
    setIsCartSummaryModalOpen,
    router,
    onCheckout,
    siteProducts,
  ]);

  const renderAafesCheckoutForm = () => {
    const checkoutParams = {
      cartAppointment,
      cartId: data.id,
      estimatedFetTaxInCents,
      installerDetails,
      productList: siteProducts,
      shippingCostInCents,
    };
    const { formId, method, inputType, inputId } = aafesCheckoutParams;
    return (
      <form id={formId} action={aafesCheckoutURL} method={method}>
        <input
          type={inputType}
          name={inputId}
          id={inputId}
          value={generateCheckoutXML(checkoutParams)}
        />
      </form>
    );
  };

  const shippingDaysForAllTires = siteProducts?.reduce(
    (prev, current) => {
      const {
        shippingDays,
        brand: { label },
        name,
        isShippingViaFreight,
      } = current;

      if (shippingDays && !isShippingViaFreight) {
        prev.push({ key: `${label} ${name}`, value: shippingDays });
      }

      return prev;
    },
    [] as Array<{ key: string; value: string }>,
  );

  const disabledCheckout =
    isLoading ||
    isShopLoading ||
    isCartShippingDataLoading ||
    isAddingToCart ||
    isApiLoading;

  const disableShipToMe =
    isShipToMeTabSelected &&
    activeOption === ShippingType.FEDEX &&
    (!pickupLocations || !pickupLocations.length);

  const closeNotification = () => {
    setIsDisplayErrorNotification(false);
  };

  const closeHasTaxExemptNotification = () => {
    setShowTaxExemptNotification(false);
  };

  // This will be used when shipping type is not a HOME
  const shippingLocationProps: ShippingLocationProps | undefined =
    (cartShipping &&
      isValidCartShipping(cartShipping.cartShipping) && {
        addressLine1: cartShipping.cartShipping.addressLine1,
        addressLine2: cartShipping.cartShipping.addressLine2 || '',
        city: cartShipping.cartShipping.city,
        isMobileInstall:
          cartShipping?.cartShipping.installer?.isMobileInstall ?? false,
        name:
          cartShipping.shippingOption === ShippingType.HOME
            ? cartShipping.cartShipping.firstName +
              ' ' +
              cartShipping.cartShipping.lastName
            : (cartShipping.cartShipping.companyName ?? ''),
        onEdit: undefined,
        state: cartShipping.cartShipping.state,
        zip: cartShipping.cartShipping.zip,
      }) ||
    undefined;

  const shippingCost =
    cartShipping?.shippingOption === ShippingType.HOME
      ? cartSummary?.shippingCostInCents || 0
      : shippingCostInCents;

  let shippingCostInCentsInCartSummary =
    (isSHCostTBD && userType !== USER_TYPE.SPECIAL_ORDER) ||
    (isNonContinental &&
      shippingCostInCents === 0 &&
      userType !== USER_TYPE.SPECIAL_ORDER)
      ? 'TBD'
      : isNonContinental &&
          (cartSummary?.shippingCostInCents === 0 ||
            cartSummary?.shippingCostInCents === null)
        ? 'Fees apply'
        : (isSHCostFree &&
              !isNonContinental &&
              userType !== USER_TYPE.SPECIAL_ORDER) ||
            (cartSummary?.coreShipHandlingPerTireInCents === 0 &&
              cartSummary?.coreShipOversizeFeePerTireInCents === 0)
          ? 'FREE'
          : shippingCost
            ? formatDollars(shippingCost)
            : 'FREE';

  if (isWHolesaleUserHasAddressDefault && shippingCostInCents) {
    shippingCostInCentsInCartSummary = formatDollars(shippingCostInCents);
  }

  const hasToDisableCheckout =
    !isCheckoutPage && (disabledCheckout || disableShipToMe || isPayLoading);

  const totalQty =
    siteProducts.length > 0
      ? calculateTotalNumberOfTiresInCart(siteProducts)
      : 0;

  const formattedTireInstallationPrice = useMemo(() => {
    if (isSourcePirelliWidget) {
      return cartAppointment
        ? installerDetails?.priceList[0]?.salePriceInCents
          ? installerDetails?.priceList[0]?.salePriceInCents
          : '0'
        : null;
    }
    return installerDetails?.priceList[0]?.salePriceInCents;
  }, [cartAppointment, installerDetails?.priceList, isSourcePirelliWidget]);

  const formattedSubTotalInCents = useMemo(() => {
    if (isSourcePirelliWidget) {
      return cartAppointment
        ? subTotalInCents
        : installerDetails?.priceList[0]?.salePriceInCents
          ? subTotalInCents -
            Number(installerDetails?.priceList[0]?.salePriceInCents || '0') *
              totalQty
          : subTotalInCents;
    }
    return subTotalInCents;
  }, [
    cartAppointment,
    installerDetails?.priceList,
    isSourcePirelliWidget,
    subTotalInCents,
    totalQty,
  ]);

  const isLinkToTRCProm = useMemo(
    () =>
      cartSummary?.siteProducts
        .flatMap(
          (product) =>
            product.siteProductPromotion as unknown as PromoListItem[],
        )
        .filter((promo) => promo.linkToTRCProm && promo.alreadyAppliedIntoCart),
    [cartSummary?.siteProducts],
  );

  const formattedTotalInCents = useMemo(() => {
    if (isSourcePirelliWidget) {
      return cartAppointment
        ? totalInCents
        : installerDetails?.priceList[0]?.salePriceInCents
          ? totalInCents -
            Number(installerDetails?.priceList[0]?.salePriceInCents || '0') *
              totalQty
          : totalInCents;
    }
    return totalInCents;
  }, [
    cartAppointment,
    installerDetails?.priceList,
    isSourcePirelliWidget,
    totalInCents,
    totalQty,
  ]);

  const total = formattedTotalInCents
    ? ` • ${formatDollars(formattedTotalInCents + (aafesCid && isLinkToTRCProm ? isLinkToTRCProm?.length : 0))}`
    : '';
  let installerSiteUrl;
  let siteName;

  if (isClient()) {
    installerSiteUrl = seStorage.getItem(
      SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_SITE_URL] as string,
    );
    siteName = seStorage.getItem(
      SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE] as string,
    );
  }

  const updateRoadHazardCost =
    trcOption === 'oneYear'
      ? cartSummary?.cartRoadHazard1YearCostInCents
      : roadHazardCostInCents;

  return (
    <div>
      <BottomCardModal
        contentLabel="cart-summary-modal"
        onClose={handleClose}
        isOpen={isOpen}
        customContentStyles={styles.modal}
        customRootStyles={styles.modalContainer}
        overlayZIndex={Z_INDEX.MODAL - 15}
        contentRef={(node) => (modalContentRef.current = node)}
      >
        <LocationModal
          isOpen={isLocationModalOpen}
          onClose={toggleLocationModal}
        />
        <div css={styles.root} ref={root}>
          <h3 css={styles.title}>{ui('cart.cartSummaryModal.title')}</h3>
          {installerSiteUrl && (
            <a css={styles.backContainer} href={installerSiteUrl}>
              <Icon name={ICONS.ARROW_LEFT} />
              <h3>Back to {siteName}</h3>
            </a>
          )}

          <h6 css={styles.tiresTitle}>Tires</h6>
          <div css={styles.tiresWrapper}>
            {!isAddingToCart &&
              siteProducts.map((product, index) => {
                const dealDetails = getDealDetails(data, product.quantity);

                return (
                  <div key={product.productId + index} css={styles.tireWrapper}>
                    <ProductInCart
                      product={product}
                      onRemove={handleRemoveItemInCart}
                      confirmCallback={onUpdateQuantity}
                      hasInstaller={
                        !!(installerDetails && installerDetails?.installerId)
                      }
                      dealDetails={dealDetails}
                      onCloseCart={onClose}
                      userType={userType}
                      isDealerTire={isDealerTire}
                      isOrderSummary={false}
                      maxQuantityAllowedForCart={
                        product.maxQuantityAllowedForCart
                      }
                      minQuantityAllowedForCart={
                        product.minQuantityAllowedForCart
                      }
                      savingCost={
                        installerDetails && installerDetails?.savingCost
                      }
                      selectedInstaller={
                        installerDetails && installerDetails?.installerId
                      }
                      isOnCartSummaryModal
                      disabledRemove={disabledCheckout}
                    />
                  </div>
                );
              })}
            {isAddingToCart &&
              prevCartProducts.map((product, index) => {
                const dealDetails = getDealDetails(data, product.quantity);

                return (
                  <div key={product.productId + index} css={styles.tireWrapper}>
                    <ProductInCart
                      product={product}
                      onRemove={handleRemoveItemInCart}
                      confirmCallback={onUpdateQuantity}
                      hasInstaller={
                        !!(installerDetails && installerDetails?.installerId)
                      }
                      dealDetails={dealDetails}
                      onCloseCart={onClose}
                      userType={userType}
                      isOrderSummary={false}
                      maxQuantityAllowedForCart={
                        product.maxQuantityAllowedForCart
                      }
                      minQuantityAllowedForCart={
                        product.minQuantityAllowedForCart
                      }
                      savingCost={
                        installerDetails && installerDetails?.savingCost
                      }
                      selectedInstaller={
                        installerDetails && installerDetails?.installerId
                      }
                      isOnCartSummaryModal
                      disabledRemove={disabledCheckout}
                    />
                  </div>
                );
              })}
            {isAddingToCart && <ProductInCartSkeleton />}
            {!isAddingToCart && (
              <div ref={serviceContainerRef}>
                <DealsAndRebatsInCartContainer />
                <ShippingServiceContainer
                  isOnModal
                  isDealerTire={isDealerTire}
                  isInstallErrorStateModalOpen={isInstallErrorStateModalOpen}
                  isTireReplacementCoverageAvailable={
                    isTireReplacementCoverageAvailable
                  }
                  setInstallErrorStateModalOpen={setInstallErrorStateModalOpen}
                  toggleInstallErrorStateModal={toggleInstallErrorStateModal}
                  installErrorStateFrom={installErrorStateTrigger}
                  deliveryMethods={deliveryMethods}
                />
              </div>
            )}
          </div>
          <h6 css={styles.tiresTitle}>Breakdown</h6>
          <div css={styles.breakdown}>
            <Breakdown
              products={siteProducts}
              tireInstallationPrice={formattedTireInstallationPrice}
              isRoadHazardChecked={!!isRoadHazardChecked}
              tireReplacementPrice={updateRoadHazardCost}
              isTireReplacementCoverageAvailable={
                isTireReplacementCoverageAvailable
              }
              hasRoadAssistance={
                hasRoadsideAssistance && displayRoadsideAssistance
              }
              siteCartCoupons={siteCartCoupons}
              isOTS={isOTS}
              trcOption={isOTS ? '' : trcOption}
            />
          </div>
          <CartSummary
            estimatedStateFeesInCents={estimatedStateFeesInCents}
            estimatedTaxInCents={estimatedTaxInCents}
            idMeVerifiedStatus={idMeVerifiedStatus}
            coupons={siteCartCoupons}
            idMeDiscountInCents={idMeDiscountInCents}
            estimatedFetTaxInCents={estimatedFetTaxInCents}
            shippingDays={
              shippingDaysForAllTires.length > 0
                ? shippingDaysForAllTires
                : undefined
            }
            roadHazardCostInCents={roadHazardCostInCents}
            shippingCostInCents={shippingCostInCentsInCartSummary}
            totalInCents={formattedTotalInCents}
            savingTodayInCents={savingTodayInCents}
            onRemovePromoCode={onRemovePromoCode}
            onUpdateVerifyStatus={onUpdateVerifyStatus}
            onApplyPromoCode={onApplyPromoCode}
            subTotalInCents={formattedSubTotalInCents}
            hasRoadsideAssistance={hasRoadsideAssistance}
            displayRoadsideAssistance={false}
            showShippingLocation={!hasFreight}
            shippingLocationData={shippingLocationProps}
            eligibleManualPromoCoupons={eligibleManualPromoCoupons}
            idMeEligibleDiscountsInCents={idMeEligibleDiscountsInCents}
            oversizedFeePerTireInCents={
              cartSummary?.coreShipOversizeFeePerTireInCents || 0
            }
            shippingCostPerTireInCents={
              cartSummary?.coreShipHandlingPerTireInCents || 0
            }
            coreShipHandlingFeesApplied={
              cartSummary?.coreShipHandlingFeesApplied
            }
            numberOfOversizedTire={cartSummary?.oversizeTireQuantity || 0}
            shipHandlingTireQuantity={
              cartSummary?.shipHandlingTireQuantity || 0
            }
            coreOverSizedFeesApplied={cartSummary?.coreOverSizedFeesApplied}
            isCouponReplacementMessage={isCouponReplacementMessage}
            couponReplacementCoupons={couponReplacementCoupons}
            isOccupationDiscountsAvailable={isOccupationDiscountsAvailable}
            isDealerTire={isDealerTire}
            totalSalePriceAfterSimpleCrew={totalSalePriceAfterSimpleCrew}
            totalSalePriceBeforeSimpleCrew={totalSalePriceBeforeSimpleCrew}
            isOTS={isOTS}
            estimatedProfitInCents={estimatedProfitInCents}
          />
          <NotificationList
            brandNotAssociatedAlert={
              cartSummary?.brandNotAssociatedAlert ?? false
            }
            showRemoveShop={showShopRemovedAlert}
            hasPriceChangedAlert={priceChangeAlert}
            delayDays={delayDays}
            delayAlert={delayAlert}
            hasUninstallable={showInstallationUnavailableAlert}
            hasFreight={hasFreight}
            hasOversize={hasOversize}
            onCloseUnavailableAlert={onCloseUnavailableAlert}
            isShowingRemoveInstallationAlert={isShowingRemoveInstallationAlert}
            onCloseRemoveInstallationAlert={onCloseRemoveInstallationAlert}
            containsTrailerTires={containsTrailerTires}
          />

          <div css={styles.intersection} aria-hidden />
          {!isCheckoutPage &&
            !isSourcePirelliWidget &&
            !isDealerTire &&
            !isSimpleShop &&
            !isOTS && (
              <div css={styles.affirmWrapper}>
                <PayLaterBannerContainer paymentType="Affirm" />
                <AffirmPromoBar
                  pageType={AffirmPageType.PRODUCT}
                  pageSource={AffirmSteerSource.CART_SUMMARY}
                  dataAmount={totalInCents}
                  extraStyles={styles.affirmContainer}
                />
              </div>
            )}
        </div>
        {/* we need to make an independent component for check out CTAs */}
        <div
          css={[
            styles.buttonWrapper,
            stickyStyles[stickyTheme],
            stickCtaStyles[stickyTheme],
            // Adding negative margin to the first div i.e notification container to cover the gap between button columns
            isDisplayErrorNotification && styles.notificationColumnContainer,
          ]}
          ref={target}
        >
          {showTaxExemptNotification && (
            <div>
              <NotificationContainer
                containerStyle={styles.notificationContainer}
                delay={1000}
                customNotificationClose={closeHasTaxExemptNotification}
              >
                <Notification
                  title={ui(
                    'cart.cartSummaryModal.alertList.hasTaxExemptNotification.title',
                  )}
                  subtext={ui(
                    'cart.cartSummaryModal.alertList.hasTaxExemptNotification.subText',
                  )}
                  icon={{
                    svgId: ICONS.BELL,
                    type: ICON_IMAGE_TYPE.ICON,
                  }}
                  type="Cart appointment error"
                  id="cart-appointment-error"
                  suppressFromHomePage={false}
                  theme={THEME.DARK}
                  handleNotificationClick={closeHasTaxExemptNotification}
                  customStyles={styles.notification}
                />
              </NotificationContainer>
            </div>
          )}
          {isDisplayErrorNotification && (
            <div>
              <NotificationContainer
                containerStyle={styles.notificationContainer}
                delay={1000}
                customNotificationClose={closeNotification}
              >
                <Notification
                  title={ui(
                    'cart.cartSummaryModal.alertList.somethingMissing.title',
                  )}
                  subtext={ui(
                    'cart.cartSummaryModal.alertList.somethingMissing.subText',
                  )}
                  icon={{
                    svgId: ICONS.BELL,
                    type: ICON_IMAGE_TYPE.ICON,
                  }}
                  type="Cart appointment error"
                  id="cart-appointment-error"
                  suppressFromHomePage={false}
                  theme={THEME.DARK}
                  handleNotificationClick={closeNotification}
                  customStyles={styles.notification}
                />
              </NotificationContainer>
            </div>
          )}
          {aafesCid && aafesCheckoutURL && renderAafesCheckoutForm()}
          <Button
            theme={THEME.LIGHT}
            onClick={handleCheckout}
            css={[
              styles.button,
              isApplePayEnabled && !isSourcePirelliWidget
                ? styles.adjustButtonSize
                : undefined,
              isSourcePirelliWidget && styles.buttonPirelli,
            ]}
            form={aafesCheckoutParams.formId}
            data-test-id="new-checkout"
            isDisabled={hasToDisableCheckout}
          >
            {hasToDisableCheckout ? (
              <Loading theme={THEME.DARK} />
            ) : (
              <span>
                {!isSourcePirelliWidget && (
                  <Icon name={ICONS.LOCK} css={[styles.iconStyle]} />
                )}
                <span>
                  {ui('cart.cartSummaryModal.checkout')}{' '}
                  {experimentId4 ? '' : total}
                </span>
              </span>
            )}
          </Button>
          <QuickCheckoutWithBraintree
            aafesCid={aafesCid}
            isApplePayEnabled={isApplePayEnabled}
            isCheckoutPage={isCheckoutPage}
            isOTS={isOTS}
            isSourcePirelliWidget={isSourcePirelliWidget}
            cartInstallable={cartInstallable}
            disabledCheckout={disabledCheckout}
            modalContentRef={modalContentRef}
            rootElement={rootElement}
            targetElement={targetElement}
            serviceContainerRef={serviceContainerRef}
            setStickyTheme={setStickyTheme}
            setInstallErrorStateModalOpen={setInstallErrorStateModalOpen}
            setInstallErrorStateTrigger={setInstallErrorStateTrigger}
            setIsApplePayEnabled={setIsApplePayEnabled}
            stickyTheme={stickyTheme}
          />
        </div>

        {!isSourcePirelliWidget && (
          <div css={styles.legalCopyPayPalApplePay}>
            {isDealerTire ? (
              <Markdown css={styles.termsOfSale}>
                {ui('checkout.termsOfSaleApplePaypalDealerTire')}
              </Markdown>
            ) : isOTS ? (
              <span css={styles.termsOfSale}>
                <span>{ui('checkout.termsOfSaleOTS1')}</span>
                <BaseLink href={'/sales'} isExternal>
                  <span>
                    <strong>terms of sale.</strong>
                  </span>
                </BaseLink>
                <span> {ui('checkout.termsOfSaleOTS2', { otsName })}</span>
              </span>
            ) : (
              <span css={styles.termsOfSale}>
                <span>{ui('checkout.termsOfSaleApplePaypal1')}</span>
                <BaseLink href={'/sales'} isExternal>
                  <span>
                    <strong>terms of sale.</strong>
                  </span>
                </BaseLink>
                <span>{ui('checkout.termsOfSaleApplePaypal2')}</span>
              </span>
            )}
          </div>
        )}
        <ContinueShopping setIsPriceChanged={setIsPriceChanged} />
        {!isSourcePirelliWidget && !isDealerTire && !isOTS && (
          <div css={styles.PurchaseIncludesContainer}>
            <PurchaseIncludesValueProp />
          </div>
        )}
        <Feedback />
        <Footer
          customStyles={styles.footer}
          isDealerTire={isDealerTire}
          isOTS={isOTS}
        />
      </BottomCardModal>
    </div>
  );
}

export default CartSummaryModal;
