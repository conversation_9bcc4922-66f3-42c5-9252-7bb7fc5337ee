'use client';

import dynamic from 'next/dynamic';
import { usePathname, useSearchParams } from 'next/navigation';
import { memo, Suspense, useCallback, useEffect, useMemo, useRef } from 'react';

import { useSearchContextSelector } from '~/components/modules/Search/Search.context';
import { useSearchModalContextSelector } from '~/components/modules/Search/SearchModal.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteSearchFilterPill } from '~/data/models/SiteSearchFilterPill';
import { SiteSearchResultActionQuery } from '~/data/models/SiteSearchResultActionQuery';
import useAsPath from '~/hooks/useAsPath';
import useRouteName from '~/hooks/useRouteName';
import useRouter from '~/hooks/useRouter';
import { apiCjToSetCookie } from '~/lib/api/cj';
import { apiPromotionName } from '~/lib/api/promotion-name';
import { apiGetSearchTypeahead } from '~/lib/api/search';
import { FS_EVENT_NAMES } from '~/lib/constants/fullstory';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import {
  ROUTE_MAP,
  ROUTE_MAP_REVERSE,
  ROUTES,
  ROUTES_PAGE_TYPE_MAP,
} from '~/lib/constants/routes';
import { USER_TYPE } from '~/lib/constants/sso';
import GA from '~/lib/helpers/analytics';
import {
  getSearchParams,
  getSearchParamsObject,
} from '~/lib/helpers/app-routes/search-params';
import { setFSCustomEvent } from '~/lib/helpers/fullstory';
import logger from '~/lib/helpers/logger';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack/index';
import { loStorage } from '~/lib/utils/browser-storage';
import { isEPPDeployment } from '~/lib/utils/deploy';
import { handleHelpCenterBackPress } from '~/lib/utils/gladly/gladly.utils';

import { initialSearchTireTypeData } from '../../Search/Search.data';
import { SearchActionType } from '../../Search/Search.types';
import {
  ID_ME_CALLBACK_URL,
  PAGE_PATHS_NOT_TO_SCROLL_TOP,
  QUERY_PARAMS_NOT_TO_SCROLL_TOP,
  TIRE_TYPE_KEYS,
} from '../App.constants';

const IterableEffect = dynamic(() => import('./IterableEffect'));

export interface digiohInputParams {
  // button selector for jQuery
  buttonAction: string;
  // what the button will do after this handler
  buttonFieldNameDl: string;
  // current value of associated field, if any
  buttonFieldValAfterClick: string;
  // name of your function
  buttonName: string;
  // page this button is on
  buttonSettings: { [key: string]: string };
  // next page if any, from button or form submit
  currentPage: string;
  fName: string;
  // if button sets a field, this is its name
  fieldValBeforeClick: string;
  // button that was clicked
  jquerySelector: string;
  // value that will be set, if any
  nextPage: string; // settings object for button (advanced users only!)
}
declare global {
  interface Window {
    openSearchModalWithPromoId: (digiohProps: digiohInputParams) => void;
  }
}

const NotScrollTopOnRouteChangeEffect = memo(() => {
  const pathname = usePathname();
  const routeName = useRouteName();

  const shouldNotScrollTopOnRouteChange = useCallback(() => {
    const searchParams = getSearchParams();
    return (
      PAGE_PATHS_NOT_TO_SCROLL_TOP.indexOf(routeName ?? '') > -1 ||
      QUERY_PARAMS_NOT_TO_SCROLL_TOP.some((key: string) =>
        searchParams?.has(key),
      )
    );
  }, [routeName]);

  useEffect(() => {
    if (shouldNotScrollTopOnRouteChange()) {
      return;
    } else {
      setTimeout(() => window.scrollTo(0, 0));
    }
  }, [pathname, shouldNotScrollTopOnRouteChange]);

  return null;
});

const FiltersEffect = memo(() => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const { setFilterPills, setRouteQueryParamOptions } =
    useSearchContextSelector((v) => ({
      setFilterPills: v.setFilterPills,
      setRouteQueryParamOptions: v.setRouteQueryParamOptions,
    }));
  const setIsSearchOpen = useSearchModalContextSelector(
    (v) => v.setIsSearchOpen,
  );

  const handleIDMECallback = useCallback(
    (url: string) => {
      const [, queryString] = url.split('?');
      const queries = new URLSearchParams(queryString);

      if (queries.get('code')) {
        loStorage.setItem(
          LOCAL_STORAGE[PROPERTIES.ID_ME_VERIFICATION_CODE],
          queries.get('code') as string,
        );

        const callbackUrl = loStorage.getItem(
          LOCAL_STORAGE[PROPERTIES.ID_ME_CALLBACK_URL],
        );
        if (callbackUrl) {
          router.push(callbackUrl);
        }
      }
    },
    [router],
  );

  const fetchFilters = useCallback(async () => {
    const [, searchFilters] = window.location.hash.split('#');

    if (searchFilters) {
      if (searchFilters.includes(ID_ME_CALLBACK_URL)) {
        handleIDMECallback(searchFilters);
      }
      const queries = new URLSearchParams(searchFilters);
      const params: Record<string, string> = {};
      const filterPills: SiteSearchFilterPill[] = [];

      for (const key of queries.keys()) {
        params[key] = queries.get(key) as string;
      }

      if (params.brand) {
        filterPills.push({
          type: 'brand',
          label: params.brand,
        });
      }
      if (params.category || params.subtype) {
        const { queryText, queryType } =
          initialSearchTireTypeData.action as SiteSearchResultActionQuery;
        const apiSearchResults = await apiGetSearchTypeahead({
          additionalQueryText: '',
          queryText,
          queryType,
        });

        TIRE_TYPE_KEYS.filter((key: string) => params[key]).forEach(
          (key: string) => {
            const query = `${key}=${params[key]}`;
            apiSearchResults.siteSearchResultGroupList.some((resultGroup) => {
              return resultGroup.siteSearchResultList.some((result) => {
                const { action } = result;
                if (
                  action.type === SearchActionType.LINK &&
                  action.link.href === query &&
                  result.label
                ) {
                  filterPills.push({
                    type: key,
                    label: result.label,
                  });
                  return true;
                }
                return false;
              });
            });
          },
        );
      }

      if (filterPills.length) {
        setFilterPills(filterPills);

        setRouteQueryParamOptions({
          routes: [
            ROUTE_MAP[ROUTES.VEHICLE_CATALOG],
            ROUTE_MAP[ROUTES.TIRE_SIZE_CATALOG_OR_CATEGORY],
          ],
          params,
        });

        setIsSearchOpen(true);
      }
    }
  }, [
    setIsSearchOpen,
    setFilterPills,
    setRouteQueryParamOptions,
    handleIDMECallback,
  ]);

  useEffect(() => {
    if (!pathname?.includes('/paid') && !pathname?.includes('/p/')) {
      fetchFilters();
    }
  }, [
    // add new pathname and searchParams in useEffect dependencies to make sure fetchFilters will run when page url is updated. it's nearly same as what we added `asPath` in dependencies of useCallback for fetchFilters function.
    pathname,
    searchParams,
    fetchFilters,
  ]);

  return null;
});

const SearchModalEffect = memo(() => {
  const { setIsSearchOpen } = useSearchModalContextSelector((v) => ({
    setIsSearchOpen: v.setIsSearchOpen,
  }));
  const { setRouteQueryParamOptions, setFilterPills } =
    useSearchContextSelector((v) => ({
      setRouteQueryParamOptions: v.setRouteQueryParamOptions,
      setFilterPills: v.setFilterPills,
    }));

  const handleHashChange = useCallback(() => {
    const params: Record<string, string> = {};
    const hash = window.location.hash;

    if (hash.includes('openSearch')) {
      const promoId = new URLSearchParams(hash.replace('#', '?')).get('promo');

      if (promoId) {
        params.promotion = promoId.toString();

        setFilterPills([
          {
            type: 'promotion',
            label: promoId, // Include promo ID in the label, or adjust as needed
          },
        ]);
      } else {
        setFilterPills([]); // Clear filter pills if no promoId is present
      }

      setRouteQueryParamOptions({
        routes: [
          ROUTE_MAP[ROUTES.VEHICLE_CATALOG],
          ROUTE_MAP[ROUTES.TIRE_SIZE_CATALOG_OR_CATEGORY],
        ],
        params,
      });

      setIsSearchOpen(true); // Open search modal
    }
  }, [setFilterPills, setIsSearchOpen, setRouteQueryParamOptions]);

  const handleDigiohButtonClick = (digiohProps?: digiohInputParams) => {
    logger.info(digiohProps);
    if (
      digiohProps &&
      digiohProps.buttonFieldValAfterClick &&
      digiohProps.buttonFieldValAfterClick !== ''
    ) {
      window.location.href = `#openSearch&promo=${digiohProps.buttonFieldValAfterClick}`;
    } else {
      window.location.href = '#openSearch';
    }
  };

  useEffect(() => {
    handleHashChange(); // Run on mount to handle initial hash
    window.openSearchModalWithPromoId = handleDigiohButtonClick;
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [handleHashChange]);

  return null; // Effect-only component
});

const PromotionsEffect = memo(() => {
  const router = useRouter();
  const { setFilterPills, setRouteQueryParamOptions } =
    useSearchContextSelector((v) => ({
      setFilterPills: v.setFilterPills,
      setRouteQueryParamOptions: v.setRouteQueryParamOptions,
    }));
  const setIsSearchOpen = useSearchModalContextSelector(
    (v) => v.setIsSearchOpen,
  );

  const fetchPromotion = useCallback(async () => {
    const searchParams = getSearchParams();
    const promotionId = searchParams?.get('promotionId');
    if (!promotionId) {
      return;
    }

    const response = await apiPromotionName({
      promotion: promotionId,
    });

    if (response.isSuccess) {
      const params = { promotion: response.data.promotionId } as Record<
        string,
        string
      >;

      setRouteQueryParamOptions({
        routes: [
          ROUTE_MAP[ROUTES.VEHICLE_CATALOG],
          ROUTE_MAP[ROUTES.TIRE_SIZE_CATALOG_OR_CATEGORY],
        ],
        params,
      });
      setFilterPills([
        {
          type: 'promotion',
          label: response.data.promotionName,
        },
      ]);
      setIsSearchOpen(true);
    } else {
      // Redirects to homepage if promotionId is invalid
      router.push(ROUTE_MAP[ROUTES.HOME]);
    }
  }, [router, setIsSearchOpen, setFilterPills, setRouteQueryParamOptions]);

  useEffect(() => {
    fetchPromotion();
  }, [fetchPromotion]);

  return null;
});

const ScrollRestorationEffect = memo(() => {
  // Scroll restoration happens too early https://github.com/vercel/next.js/issues/3303
  useEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }
  }, []);

  return null;
});

const RudderstackScrollEventEffect = memo(() => {
  const routeName = useRouteName();

  const pageScrolledRef = useRef<number>(24);
  const scrolledPageRef = useRef('');

  const getScrollPercent = useCallback(() => {
    const h = document.documentElement,
      b = document.body,
      st = 'scrollTop',
      sh = 'scrollHeight';

    const scrollPercent = Math.round(
      ((h[st] || b[st]) / ((h[sh] || b[sh]) - h.clientHeight)) * 100,
    );
    //get the percentage of scroll

    return isNaN(scrollPercent) ? '' : scrollPercent;
  }, []);

  const onScroll = useCallback(() => {
    const scrollNumber = getScrollPercent();
    if (routeName && scrolledPageRef.current != routeName) {
      scrolledPageRef.current = routeName;
      pageScrolledRef.current = 24;
    }
    if (scrollNumber && scrollNumber > pageScrolledRef.current) {
      const query = getSearchParamsObject();
      rudderstackSendTrackEvent(
        RudderstackTrackEventName.PAGE_SCROLL_MILESTONE,
        {
          name: window.location.href ?? '',
          page_scroll_milestone: pageScrolledRef.current + 1,
          path: routeName ?? '',
          query: query ?? '',
          title: document.title ?? '',
          url: window.location.href ?? '',
        },
      );
      pageScrolledRef.current += 25;
    }
  }, [getScrollPercent, routeName]);

  useEffect(() => {
    //add eventlistener to window
    window.addEventListener('scroll', onScroll, false);

    // remove event on unmount to prevent a memory leak with the cleanup
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [onScroll]);

  return null;
});

const GladlyEffect = memo(() => {
  useEffect(() => {
    window.onload = handleHelpCenterBackPress;
  }, []);

  return null;
});

const CJCookieEffect = memo(() => {
  const setCjCookie = useCallback(async () => {
    const searchParams = getSearchParams();
    const cjevent = searchParams?.get('cjevent');
    if (cjevent && typeof cjevent === 'string') {
      await apiCjToSetCookie(cjevent);
    }
  }, []);

  useEffect(() => {
    setCjCookie();
  }, [setCjCookie]);

  return null;
});

const IterableEffectContainer = memo(() => {
  const { userDetail, userType } = useUserPersonalizationContextSelector(
    (v) => ({
      userDetail: v.userDetail,
      userType: v.userType,
    }),
  );

  return !userDetail || userType === USER_TYPE.NONE ? null : (
    <IterableEffect email={userDetail.username} />
  );
});

function PageTypeGAEvent() {
  const asPath = useAsPath();
  const routeName = useRouteName() ?? '';

  useEffect(() => {
    let routeKey = ROUTE_MAP_REVERSE[routeName];
    if (routeKey === ROUTES.PRODUCT_DETAIL && !asPath.includes('#')) {
      routeKey = ROUTES.PRODUCT_LINE;
    }
    if (routeKey) {
      const pageType = ROUTES_PAGE_TYPE_MAP[routeKey];

      GA.addToDataLayer({
        event: 'PageType',
        pageType,
      });
    }
  }, [asPath, routeName]);

  return null;
}

function PreviousUrl() {
  useEffect(() => {
    const path = window.location.pathname;
    if (path.indexOf('/sso-redirect') < 0) {
      loStorage.setItem('sso_redirect_last_url', path);
    }
  }, []);

  return null;
}

function DealerTireEffect() {
  const { accountType, company } = useUserPersonalizationContextSelector(
    (v) => ({
      accountType: v.accountType,
      company: v.company,
    }),
  );

  const employeeProgram = useMemo(() => {
    return {
      accountTypeCompany: company,
      accountType,
    };
  }, [company, accountType]);

  useEffect(() => {
    setFSCustomEvent(FS_EVENT_NAMES.EMPLOYEE_PROGRAM, employeeProgram);
  }, [employeeProgram]);

  return null;
}

export default function GlobalEffect() {
  const isEPPDeploy = isEPPDeployment();

  return (
    <>
      <NotScrollTopOnRouteChangeEffect />
      <Suspense fallback={null}>
        <FiltersEffect />
        <PageTypeGAEvent />
        <PreviousUrl />
      </Suspense>
      <PromotionsEffect />
      <ScrollRestorationEffect />
      <RudderstackScrollEventEffect />
      <GladlyEffect />
      <CJCookieEffect />
      {isEPPDeploy ? null : <IterableEffectContainer />}
      {isEPPDeploy && <DealerTireEffect />}
      <SearchModalEffect />
    </>
  );
}
