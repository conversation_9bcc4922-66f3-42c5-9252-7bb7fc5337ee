'use client';

import { usePathname } from 'next/navigation';
import Script from 'next/script';
import { useEffect } from 'react';

import {
  CONSTANTS as FEEDBACK_CONSTANTS,
  showFeedbackTab,
} from '~/components/global/Feedback/Feedback.utils';
import { useGladlyWidgetScriptContextSelector } from '~/context/ThirdPartyScriptsContext/AllGladlyScriptsContext/GladlyWidgetScript.context';
import { useFeedbackifyScriptContextSelector } from '~/context/ThirdPartyScriptsContext/FeedbackifyScript.context';
import { useGTMScriptContextSelector } from '~/context/ThirdPartyScriptsContext/GTMScript.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { ACCOUNT_TYPE } from '~/lib/constants/sso';
import { GTM_CONSTANTS } from '~/lib/helpers/analytics';
import { initESW as initDTChat } from '~/lib/helpers/salesforce';
import { initESW as initOTSChat } from '~/lib/helpers/salesforce-ots';
import {
  isEPPDeployment,
  isOTSDeployment,
  isSimpleShopDeployment,
} from '~/lib/utils/deploy';
import {
  getGladlySnippet,
  CONSTANTS as GLADLY_CONSTANTS,
} from '~/lib/utils/gladly/gladly.utils';

function GTMScript() {
  const onGTMScriptLoad = useGTMScriptContextSelector((v) => v.onGTMScriptLoad);

  return (
    <Script
      src={GTM_CONSTANTS.GTM_SCRIPT}
      onLoad={onGTMScriptLoad}
      crossOrigin="anonymous"
      strategy="afterInteractive"
    />
  );
}

function FeedbackifyScript({
  accountType,
  company,
}: {
  accountType: ACCOUNT_TYPE;
  company: string;
}) {
  const pathname = usePathname();
  const isWalmartOrderPage = pathname === ROUTE_MAP[ROUTES.WALMART_ORDER_PAGE];

  const onFeedbackifyScriptLoad = useFeedbackifyScriptContextSelector(
    (v) => v.onFeedbackifyScriptLoad,
  );

  const handleFeedbackifyScriptLoad = () => {
    onFeedbackifyScriptLoad();
    showFeedbackTab(accountType, company);
  };

  useEffect(() => {
    if (accountType && company) {
      const previousParentNode = document.getElementById('fby-screen');
      if (previousParentNode?.lastChild) {
        previousParentNode?.removeChild(previousParentNode?.lastChild);
      }
      showFeedbackTab(accountType, company);
    }
  }, [accountType, company]);

  return !isWalmartOrderPage ? (
    <Script
      src={FEEDBACK_CONSTANTS[0].fbySource}
      id={FEEDBACK_CONSTANTS[0].fbyScriptId}
      strategy="lazyOnload"
      onLoad={handleFeedbackifyScriptLoad}
    />
  ) : null;
}

function GladlyWidgetScript() {
  const onGladlyWidgetLoad = useGladlyWidgetScriptContextSelector(
    (v) => v.onGladlyWidgetLoad,
  );

  return (
    <Script
      id={GLADLY_CONSTANTS.SCRIPT_ID}
      src={GLADLY_CONSTANTS.SCRIPT_SRC}
      strategy="lazyOnload"
      onLoad={onGladlyWidgetLoad}
    />
  );
}

function GladlyWidgetExtraScript() {
  const isGladlyWidgetLoaded = useGladlyWidgetScriptContextSelector(
    (v) => v.isGladlyWidgetLoaded,
  );

  return isGladlyWidgetLoaded ? (
    <Script
      id={GLADLY_CONSTANTS.SCRIPT_EXTRA_ID}
      dangerouslySetInnerHTML={{ __html: getGladlySnippet() }}
      strategy="afterInteractive"
    />
  ) : null;
}

function GladlyScript() {
  return (
    <>
      <GladlyWidgetScript />
      <GladlyWidgetExtraScript />
    </>
  );
}

function SalesforceChatScript({
  isEPP,
  isOTS,
}: {
  isEPP: boolean;
  isOTS: boolean;
}) {
  const handleScriptLoad = () => {
    if (typeof window !== 'undefined' && window.embedded_svc) {
      if (isEPP) {
        initDTChat('https://service.force.com');
      }
      if (isOTS) {
        initOTSChat('https://service.force.com');
      }
    }
  };
  return (
    <Script
      src="https://service.force.com/embeddedservice/5.0/esw.min.js"
      onLoad={handleScriptLoad}
      strategy="afterInteractive"
    />
  );
}

function ClientComponentScript() {
  const { accountType, company } = useUserPersonalizationContextSelector(
    (v) => ({
      accountType: v.userDetail?.accountTypes[0].name,
      company: v.userDetail?.accountTypeCompany?.companyName || '',
    }),
  );
  const isOTS = isOTSDeployment();
  const isSimpleShop = isSimpleShopDeployment();
  const isEPP = isEPPDeployment();

  return (
    <>
      {!isSimpleShop && !isOTS && !isEPP && <GTMScript />}
      <FeedbackifyScript
        accountType={accountType as ACCOUNT_TYPE}
        company={company}
      />
      {!isOTS && !isEPP && <GladlyScript />}
      {(isEPP || isOTS) && <SalesforceChatScript isEPP={isEPP} isOTS={isOTS} />}
    </>
  );
}

export default ClientComponentScript;
