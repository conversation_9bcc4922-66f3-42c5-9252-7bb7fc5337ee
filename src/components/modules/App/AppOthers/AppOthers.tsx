import GladlyChatButtonContainer from '~/components/global/GladlyChatButton/GladlyChatButton.container';
import GuidedQuestionsModalContainer from '~/components/global/GuidedQuestions/GuidedQuestionModal.container';
import ContactEmailModalContainer from '~/components/global/LiveSupport/ContactEmailModal/ContactEmailModal.container';
import LoadingBar from '~/components/global/LoadingBar/LoadingBar';
import UserZipManualEntryModalContainer from '~/components/global/Modal/UserZipManualEntryModal/UserZipManualEntryModal.container';
import NotificationList from '~/components/global/NotificationBanner/NotificationList';
import SimpleScoreSpeedOMeterModalContainer from '~/components/global/SimpleScoreSpeedOMeterModal/SimpleScoreSpeedOMeterModal.container';
import AppEmailNotificationContainer from '~/components/modules/App/AppEmailNotification/AppEmailNotification.container';
import SearchModal from '~/components/modules/Search/SearchModal';
import TireSnapModalContainer from '~/components/modules/TireSnap/TireSnapModal.container';
import { SiteNotificationTypes } from '~/data/models/SiteNotificationTypes';
import { isEPPDeployment, isOTSDeployment } from '~/lib/utils/deploy';

import { styles } from './AppOthers.styles';

function AppOthers() {
  return (
    <div>
      <LoadingBar />
      <NotificationList
        types={[SiteNotificationTypes.Sale, SiteNotificationTypes.System]}
        customStyles={styles.notificationContainer}
      />
      <AppEmailNotificationContainer />
      {!isEPPDeployment() && !isOTSDeployment() && (
        <GladlyChatButtonContainer />
      )}
      <SearchModal />
      <UserZipManualEntryModalContainer />
      <GuidedQuestionsModalContainer />
      <TireSnapModalContainer />
      <ContactEmailModalContainer />
      <SimpleScoreSpeedOMeterModalContainer />
    </div>
  );
}

export default AppOthers;
