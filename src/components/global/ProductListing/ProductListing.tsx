import isStrictEqual from 'fast-deep-equal';
import { memo, MouseEvent, useCallback, useMemo, useState } from 'react';

import BrandLogoOrLabel from '~/components/global/BrandLogoOrLabel/BrandLogoOrLabel';
import Checkbox from '~/components/global/Checkbox/Checkbox';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import BaseLink from '~/components/global/Link/BaseLink';
import Prices from '~/components/global/Prices/Prices';
import PromoTag from '~/components/global/PromoTag/PromoTag';
import {
  FLEET_IMAGE,
  SPECIAL_IMAGE,
} from '~/components/modules/Cart/CartSummaryModal/CarSummaryModal.utils';
import FleetModal from '~/components/modules/Cart/CartSummaryModal/FleetModal/FleetModal';
import JoinSimpleCrewModal from '~/components/modules/JoinSimpleCrew/JoinSimpleCrewModal';
import PuncturedModal from '~/components/modules/PDP/PuncturedModal/PuncturedModal';
import ItemTitleContent from '~/components/modules/PDP/TechnicalSpecs/TechnicalSpecsList/TechnicalSpecsListItem/ItemTitleContent';
import ManufacturersWarrantiesModal from '~/components/pages/CheckoutPage/Services/ManufacturersWarrantiesModal/ManufacturersWarrantiesModal';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import {
  CurationType,
  SiteCatalogProductItem,
} from '~/data/models/SiteCatalogProductItem';
import { SiteIcon } from '~/data/models/SiteIcon';
import { DealHighlightEnum } from '~/data/models/SitePDPDealAlertProduct';
import { SitePromotionStyleEnum } from '~/data/models/SitePromotion';
import useRouteName from '~/hooks/useRouteName';
import { ICON_IMAGE_TYPE } from '~/lib/backend/icon-image.types';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { USER_DISCOUNT_BADGING, USER_TYPE } from '~/lib/constants/sso';
import { SSOUserIdResponse } from '~/lib/constants/sso.types';
import { CSSStyles, CSSStylesProp } from '~/lib/constants/styles.types';
import { THEME } from '~/lib/constants/theme';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { mapToProductDataCartItem } from '~/lib/helpers/rudderstack/transformer';
import { isOTSDeployment } from '~/lib/utils/deploy';
import { formatDollars } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';
import { uiJSX } from '~/lib/utils/ui-dictionary-jsx';

import Button from '../Button/Button';
import EmployeePrice from './EmployeePrice/EmployeePrice';
import styles, { rootStyles } from './ProductListing.styles';
import { ProductListingProps } from './ProductListing.types';
import ProductListingImage from './ProductListingImage';
import Rating from './Rating/Rating';
import ReviewTooltip from './ReviewTooltip/ReviewTooltip';
import SimpleScoreCardContainer from './SimpleScoreCard/SimpleScoreCard.container';
import TireDetails from './TireDetails/TireDetails';

interface Props {
  customContainerStyles?: CSSStyles;
  customHighlight?: DealHighlightEnum;
  customImageContainerStyle?: CSSStylesProp;
  customPromoContainerStyles?: CSSStylesProp;
  customSticker?: { color: string; title: string };
  dealsDrawer?: boolean;
  disableRedirect?: boolean;
  hideAddCTA?: boolean;
  hideHoverStyle?: boolean;
  hideSticker?: boolean;
  hideWarranty?: boolean;
  imageList: ProductListingProps['imageList'];
  index: number;
  isChecked?: boolean;
  isDealAlertProduct?: boolean;
  isDealerTire?: boolean;
  isFleet?: boolean;
  isGrouped?: boolean;
  isHighlighted?: boolean;
  isInHomePage?: boolean;
  isRecentTireGroup?: boolean;
  isSearchForTireSize?: boolean;
  isTopPicksGroup?: boolean;
  noBadgeInEveryProduct: boolean;
  noScoreInEveryProduct?: boolean;
  noVolatileInEveryProduct?: boolean;
  onCheckChange?: (product: SiteCatalogProductItem) => void;
  onConfirmSize?: (product: ProductListingProps) => void;
  onSelectSize?: (product: ProductListingProps) => void;
  openTopTireDetails?: (index: number) => void;
  product: ProductListingProps | null;
  siblingHasSimpleCrewPrice?: boolean;
  siblingHasUnlockSimpleCrewPrice?: boolean;
  testId?: string;
  theme?: THEME;
  userDetail: SSOUserIdResponse | null | undefined;
  userType: USER_TYPE;
}

const promoIcon: SiteIcon = {
  svgId: ICONS.INFO,
  type: ICON_IMAGE_TYPE.ICON,
};

function ProductListing({
  hideSticker,
  product,
  isHighlighted,
  imageList,
  isGrouped,
  isInHomePage = false,
  isSearchForTireSize = false,
  isTopPicksGroup,
  index,
  testId,
  noBadgeInEveryProduct,
  noVolatileInEveryProduct,
  openTopTireDetails,
  onCheckChange,
  isChecked,
  dealsDrawer,
  hideAddCTA,
  theme = THEME.LIGHT,
  hideHoverStyle,
  customSticker,
  customImageContainerStyle,
  customPromoContainerStyles,
  disableRedirect,
  noScoreInEveryProduct,
  isDealAlertProduct,
  userType,
  userDetail,
  customContainerStyles,
  onSelectSize,
  onConfirmSize,
  siblingHasSimpleCrewPrice,
  siblingHasUnlockSimpleCrewPrice,
  isDealerTire,
}: Props) {
  const isOTS = isOTSDeployment();
  const [isOpenFleetModal, setIsOpenFleetModal] = useState<boolean>(false);
  const [openSimpleCrewModal, setSimpleCrewOpenModal] =
    useState<boolean>(false);
  const [isOpenPuncturedModal, setIsOpenPuncturedModal] = useState(false);
  const [
    isManufacturersWarrantiesModalOpen,
    setIsManufacturersWarrantiesModalOpen,
  ] = useState<boolean>(false);
  const isHighlightedDealAlertProduct = isDealAlertProduct && index === 1;
  const isNonHighlightedDealAlertProduct = isDealAlertProduct && index === 0;
  const isRetailMember = userType === USER_TYPE.RETAIL_MEMBER;
  const isLoggedIn: boolean = userType !== USER_TYPE.NONE;

  const isSpecialOrder = userType === USER_DISCOUNT_BADGING.SPECIAL_ORDER;
  const isFleetOrder = userType === USER_DISCOUNT_BADGING.FLEET_ORDER;
  const routeName = useRouteName() ?? '';
  const isPDPPage = useMemo(
    () =>
      [
        ROUTE_MAP[ROUTES.PRODUCT_DETAIL],
        ROUTE_MAP[ROUTES.PRODUCT_DETAIL_NAME],
      ].includes(routeName),
    [routeName],
  );

  const handlePromoTagClick = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      if (openTopTireDetails) {
        openTopTireDetails(index);
      }
    },
    [openTopTireDetails, index],
  );

  const togglePuncturedModal = useCallback(() => {
    setIsOpenPuncturedModal(
      (prevIsOpenPuncturedModal) => !prevIsOpenPuncturedModal,
    );
  }, []);

  const openManufacturersWarrantiesModal = useCallback(() => {
    setIsManufacturersWarrantiesModalOpen(true);
  }, []);

  const closeManufacturersWarrantiesModal = useCallback(() => {
    setIsManufacturersWarrantiesModalOpen(false);
  }, []);

  const handleCheckContainerClick = useCallback(
    (event: MouseEvent<HTMLDivElement>) => {
      event.stopPropagation();
    },
    [],
  );

  const handleProductHrefClick = () => {
    const rSProduct = mapToProductDataCartItem(product, '', 4);
    rudderstackSendTrackEvent(RudderstackTrackEventName.PRODUCT_CLICKED, {
      ...rSProduct,
    });
  };

  const handleCheckChange = useCallback(() => {
    if (onCheckChange && product) {
      onCheckChange(product as SiteCatalogProductItem);
    }
  }, [onCheckChange, product]);

  const handleSelectSize = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      if (onSelectSize && product) {
        onSelectSize(product);
      }
    },
    [onSelectSize, product],
  );

  const handleConfirmSize = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      if (onConfirmSize && product) {
        onConfirmSize(product);
      }
    },
    [onConfirmSize, product],
  );

  const closeFleetModal = useCallback(() => {
    setIsOpenFleetModal(false);
  }, []);

  const closeSimpleCrewModal = () => {
    setSimpleCrewOpenModal(false);
  };
  if (!product) {
    return null;
  }

  const {
    activeFilterValueList,
    gridAttribute,
    brand,
    brandTier,
    ctaLabel,
    curationType,
    deliveryInfo,
    dataMomentList,
    estimateProfit,
    installMonday,
    installTomorrow,
    isRunFlat,
    priceList,
    siteCatalogPromotionInfo,
    link,
    loadRange,
    loadSpeedRating,
    name,
    rating,
    size,
    rearSize,
    partNumber,
    simpleScore,
    simpleScoreRange,
    handlDuraScore,
    handlDuraScoreRange,
    longevityScore,
    longevityScoreRange,
    tractionScore,
    tractionScoreRange,
    isDurability,
    dynamicRad,
    volatile,
    productReviewRatings,
    showSaleStrikethrough,
    showSimpleCrewStrikethrough,
    isMappedProduct = true,
    isSimpleCrewPromo,
    siteProductSpecs,
  } = product;

  const categorySpec = (siteProductSpecs || []).find(
    (spec) => spec.name === 'Category',
  );

  const vehicleSpec = (siteProductSpecs || []).find(
    (spec) => spec.name === 'Vehicle',
  );

  const warrantySpec = (siteProductSpecs || []).find(
    (spec) => spec.name === 'Mileage Warranty',
  );

  return (
    <div
      css={[
        styles.root,
        rootStyles[theme],
        isHighlighted && styles.rootHighlighted,
        isGrouped && styles.rootGrouped,
        hideHoverStyle && styles.hideHover,
        isDealAlertProduct && dealsDrawer && styles.rootUpdate,
        !isHighlightedDealAlertProduct && dealsDrawer && styles.rootDealAlert,
      ]}
      data-testid={testId}
      className="product-listing-item"
    >
      <span
        css={[
          styles.topPickBadge,
          noBadgeInEveryProduct
            ? styles.noEmptySpaceForBadge
            : styles.topPickBadgeInEveryProduct,
          customPromoContainerStyles,
        ]}
      >
        {ctaLabel && (
          <PromoTag
            style={SitePromotionStyleEnum.SitePromotionItemBlackPillRectangle}
            icon={
              curationType == CurationType.TOP_PICKS ? promoIcon : undefined
            }
            label={ctaLabel}
            handleClick={handlePromoTagClick}
            customContainerStyles={styles.promoTag}
            isCTALabel
          />
        )}
      </span>
      {!isDealerTire && (
        <span
          css={[
            styles.topPickBadgeBottom,
            isPDPPage && styles.topPickBadgeBottomPDP,
          ]}
        >
          <SimpleScoreCardContainer
            simpleScore={simpleScore ?? ''}
            simpleScoreRange={simpleScoreRange}
            handlDuraScore={handlDuraScore}
            handlDuraScoreRange={handlDuraScoreRange}
            longevityScore={longevityScore}
            longevityScoreRange={longevityScoreRange}
            tractionScore={tractionScore}
            tractionScoreRange={tractionScoreRange}
            isDurability={isDurability}
            noScoreInEveryProduct={noScoreInEveryProduct}
            isDealAlertProduct={isDealAlertProduct}
          />
        </span>
      )}
      <div css={styles.parentDivCss}>
        <div
          css={[
            styles.imageWrapper,
            isHighlighted && styles.imageWrapperHighlighted,
            customContainerStyles,
          ]}
        >
          {(onCheckChange || isPDPPage) && (
            <div css={styles.checkbox} onClick={handleCheckContainerClick}>
              <label css={styles.checkLabel}>
                <Checkbox
                  checked={isChecked}
                  onChange={handleCheckChange}
                  data-testid={'checkbox' + product.productId}
                  aria-label={ui('catalog.compare.compare')}
                />
                <span css={styles.checkTitle}>
                  {ui('catalog.compare.compare')}
                </span>
              </label>
            </div>
          )}
          <ProductListingImage
            hideAddCTA={!!onSelectSize || !!onConfirmSize ? true : hideAddCTA}
            hideSticker={hideSticker}
            imageList={imageList}
            index={index}
            isHighlighted={isHighlighted}
            isTopPicksGroup={isTopPicksGroup}
            product={product}
            dealsDrawer={dealsDrawer}
            customSticker={customSticker}
            customImageContainerStyle={customImageContainerStyle}
            customContainerStyles={customContainerStyles}
            isDealAlertProduct={isDealAlertProduct}
          />
          {onSelectSize && (
            <Button
              theme={THEME.LIGHT}
              css={styles.selectSizeCta}
              onClick={handleSelectSize}
            >
              {ui('catalog.productListing.selectSize')}
            </Button>
          )}
          {onConfirmSize && (
            <Button
              theme={THEME.LIGHT}
              css={styles.confirmSizeCta}
              onClick={handleConfirmSize}
            >
              {ui('catalog.productListing.confirmSize')}
            </Button>
          )}
          <EmployeePrice />
        </div>
        <div
          css={[
            styles.info,
            isHighlighted && styles.infoHighlighted,
            !noBadgeInEveryProduct && styles.infoBadgeProduct,
          ]}
        >
          {brand && (
            <div css={styles.brand}>
              <BrandLogoOrLabel
                brand={brand}
                isCentered={isHighlighted}
                customContainerStyles={styles.brandImage}
                customLabelStyles={styles.brandLabel}
                isNonHighlightedDealAlertProduct={
                  isNonHighlightedDealAlertProduct
                }
              />
            </div>
          )}
          <div>
            {userType === USER_TYPE.SIMPLE_SALES_TOOL &&
              dynamicRad?.class &&
              brandTier && (
                <div>
                  <h2 css={styles.dynamicRad}>
                    {`${dynamicRad?.class && `RAD ${dynamicRad.class}`}${brandTier && ` TIER ${brandTier}`}`}
                  </h2>
                  {estimateProfit && estimateProfit > 0 ? (
                    <h2 css={styles.estimateProfit}>{estimateProfit}</h2>
                  ) : null}
                </div>
              )}
          </div>
          <div css={!noVolatileInEveryProduct ? styles.volatile : ''}>
            {volatile ? ui('pdp.productInfo.volatileAvailability') : ''}
          </div>
          {gridAttribute && <span css={styles.attribute}>{gridAttribute}</span>}
          <h3 css={[styles.subcopy, styles.productNameContainer]}>
            {disableRedirect ? (
              <div
                css={[
                  styles.linkText,
                  isHighlightedDealAlertProduct && styles.linkTextBold,
                ]}
              >
                <TireDetails
                  isInHomePage={isInHomePage}
                  isSearchForTireSize={isSearchForTireSize}
                  loadRange={loadRange || null}
                  isRunFlat={isRunFlat}
                  loadSpeedRating={loadSpeedRating || null}
                  name={name || ''}
                  rearSize={rearSize || null}
                  size={size || null}
                />
              </div>
            ) : (
              <BaseLink
                css={
                  isHighlightedDealAlertProduct
                    ? styles.linkTextBold
                    : styles.linkText
                }
                onClick={handleProductHrefClick}
                href={link.href}
              >
                <TireDetails
                  isInHomePage={isInHomePage}
                  isSearchForTireSize={isSearchForTireSize}
                  loadRange={loadRange || null}
                  isRunFlat={isRunFlat}
                  loadSpeedRating={loadSpeedRating || null}
                  name={name || ''}
                  rearSize={rearSize || null}
                  size={size || null}
                  partNumber={partNumber}
                />
              </BaseLink>
            )}
          </h3>
          {priceList && priceList.length > 0 && priceList[0]?.price && (
            <div
              css={
                showSimpleCrewStrikethrough ||
                siblingHasSimpleCrewPrice ||
                (!isRetailMember &&
                  priceList &&
                  priceList[0]?.price?.salePriceAfterSimpleCrew &&
                  parseInt(priceList[0]?.price?.salePriceInCents, 10) >
                    parseInt(
                      priceList[0]?.price?.salePriceAfterSimpleCrew,
                      10,
                    )) ||
                siblingHasUnlockSimpleCrewPrice
                  ? styles.priceMarginBottom05
                  : styles.priceMarginBottom15
              }
            >
              <Prices
                priceList={priceList}
                isStartingAtPrice={!size}
                customWrapperStyles={
                  isDealAlertProduct && dealsDrawer
                    ? styles.dealAlertPrice
                    : styles.prices
                }
                customPriceStyles={
                  isNonHighlightedDealAlertProduct
                    ? styles.salePriceDealAlert
                    : (showSimpleCrewStrikethrough && isRetailMember) ||
                        showSaleStrikethrough
                      ? styles.simpleCrewPrice
                      : styles.salePrice
                }
                showSaleStrikethrough={showSaleStrikethrough}
                customOriginalStyles={styles.originalPrice}
                customNoPriceStyles={styles.noPriceStyles}
                isRetailMember={isRetailMember}
                showSimpleCrewStrikethrough={showSimpleCrewStrikethrough}
                isCatalogPage
              />
            </div>
          )}
          <div css={styles.crewPriceContainer}>
            {(isFleetOrder || isSpecialOrder) &&
              userType &&
              Number(userDetail?.accountTypes?.[0]?.discountPercent) > 0 && (
                <div css={styles.fleetPriceContainer}>
                  {userType ? (
                    <span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsOpenFleetModal(true);
                        }}
                        css={styles.fleetLable}
                      >
                        <Icon
                          name={ICONS.REVIEW_VERIFIED}
                          css={styles.fleetIconContainer}
                        />
                      </button>
                      <span
                        css={[styles.subcopy, styles.fleetPriceText]}
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsOpenFleetModal(true);
                        }}
                      >
                        {isFleetOrder
                          ? `${ui('account.fleetPriceDefault')} - ${Number(userDetail?.accountTypes?.[0]?.discountPercent).toFixed()}% off`
                          : `${ui('account.specialPricingDefault')} - ${Number(userDetail?.accountTypes?.[0]?.discountPercent).toFixed()}% off`}
                      </span>
                    </span>
                  ) : (
                    <span>
                      <Icon
                        name={ICONS.REVIEW_VERIFIED}
                        css={styles.fleetIconContainer}
                      />
                      <span css={[styles.subcopy, styles.fleetPriceText]}>
                        {ui('account.fleetPriceDefault')}
                      </span>
                    </span>
                  )}
                </div>
              )}

            {isRetailMember && isSimpleCrewPromo && (
              <div css={styles.fleetPriceContainer}>
                <button
                  aria-label={ui('catalog.productListing.unlockCrewPrice')}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSimpleCrewOpenModal(true);
                  }}
                  css={styles.fleetLable}
                >
                  <Icon
                    name={ICONS.REVIEW_VERIFIED}
                    css={styles.fleetIconContainer}
                  />
                </button>
                <span
                  css={[styles.subcopy, styles.fleetPriceText]}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSimpleCrewOpenModal(true);
                  }}
                >
                  {userType
                    ? ui(`account.fleetPrice.${userType}`)
                    : ui('account.fleetPriceDefault')}
                </span>
              </div>
            )}
            {!isRetailMember &&
              priceList &&
              !isMappedProduct &&
              priceList[0]?.price?.salePriceAfterSimpleCrew &&
              parseInt(priceList[0]?.price?.salePriceInCents, 10) >
                parseInt(priceList[0]?.price?.salePriceAfterSimpleCrew, 10) && (
                <div css={styles.fleetPriceContainer}>
                  <button
                    aria-label={ui('catalog.productListing.unlockCrewPrice')}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSimpleCrewOpenModal(true);
                    }}
                    css={styles.fleetLable}
                  >
                    <Icon name={ICONS.TAG} css={styles.unlockPriceTag} />
                  </button>
                  <span
                    css={[styles.subcopy, styles.unlockPriceText]}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSimpleCrewOpenModal(true);
                    }}
                  >
                    {uiJSX('catalog.productListing.unlockprice', {
                      discountPrice: formatDollars(
                        priceList !== null
                          ? (priceList[0]?.price?.salePriceAfterSimpleCrew ?? 0)
                          : 0,
                      ),
                    })}
                  </span>
                </div>
              )}
            {!isRetailMember &&
              priceList &&
              isMappedProduct &&
              isSimpleCrewPromo && (
                <div css={styles.fleetPriceContainer}>
                  <button
                    aria-label={ui('catalog.productListing.unlockCrewPrice')}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSimpleCrewOpenModal(true);
                    }}
                    css={styles.fleetLable}
                  >
                    <Icon name={ICONS.TAG} css={styles.unlockPriceTag} />
                  </button>
                  <span
                    css={[styles.subcopy, styles.unlockPriceText]}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSimpleCrewOpenModal(true);
                    }}
                  >
                    {ui('catalog.productListing.unlockCrewPrice')}
                  </span>
                </div>
              )}
          </div>
          <h3
            css={[
              styles.subcopy,
              isHighlightedDealAlertProduct && styles.subcopyBold,
              styles.warrantyInfo,
            ]}
          >
            <Icon name={ICONS.VERIFIED_SMALL} css={styles.warrantyInfoIcon} />
            <span>{ui('pdp.productInfo.inStock')}</span>
          </h3>
          {deliveryInfo &&
            deliveryInfo.value.length > 0 &&
            !installTomorrow?.label &&
            !installMonday?.label &&
            !isDealerTire &&
            !isOTS && (
              <h3
                css={[
                  styles.subcopy,
                  isHighlightedDealAlertProduct && styles.subcopyBold,
                  styles.warrantyInfo,
                ]}
              >
                <Icon
                  name={ICONS.SHIPPING_TRUCK}
                  css={styles.warrantyInfoIcon}
                />
                <span>{deliveryInfo.value}</span>
              </h3>
            )}
          {rating && rating.value > 0 && rating.quantity > 0 && (
            <div css={styles.ratingContainer}>
              <Rating
                isHighlightedDealAlertProduct={isHighlightedDealAlertProduct}
                rating={rating}
              />
              <ReviewTooltip
                link={link}
                productReviewRatings={productReviewRatings}
              />
            </div>
          )}

          <h3
            css={[
              styles.category,
              styles.subcopy,
              styles.toolTip0,
              isHighlightedDealAlertProduct && styles.subcopyBold,
            ]}
          >
            <span css={styles.infoIcon}>
              {categorySpec ? categorySpec?.values[0] : 'No category'}
            </span>
            <span css={styles.tooltipIcon}>
              <ItemTitleContent
                content={
                  categorySpec
                    ? String(categorySpec?.description)
                    : 'No category'
                }
                index={0}
              />
            </span>
          </h3>

          <h3
            css={[
              styles.category,
              styles.subcopy,
              styles.toolTip2,
              isHighlightedDealAlertProduct && styles.subcopyBold,
            ]}
          >
            <span css={styles.infoIcon}>
              {vehicleSpec ? vehicleSpec?.values[0] : 'No vehicle type'}
            </span>
            <span css={styles.tooltipIcon}>
              <ItemTitleContent
                content={
                  vehicleSpec
                    ? String(vehicleSpec?.description)
                    : 'No vehicle type'
                }
                index={1}
              />
            </span>
          </h3>

          <h3
            css={[
              styles.category,
              styles.subcopy,
              styles.toolTip2,
              isHighlightedDealAlertProduct && styles.subcopyBold,
            ]}
          >
            <span css={styles.infoIcon}>
              {warrantySpec && warrantySpec.values[0] !== 'N/A'
                ? `${warrantySpec?.values[0]} mile warranty`
                : 'No mileage warranty'}
            </span>
            <span css={styles.tooltipIcon}>
              <ItemTitleContent
                content={
                  warrantySpec && warrantySpec.values[0] !== 'N/A'
                    ? String(warrantySpec?.description)
                    : 'This product is not covered by a manufacturer’s treadlife warranty.'
                }
                index={2}
              />
            </span>
          </h3>

          {isRunFlat && (
            <span
              css={[
                styles.subcopy,
                styles.runFlat,
                isHighlightedDealAlertProduct && styles.subcopyBold,
              ]}
            >
              <span>{ui('catalog.productListing.runFlat')}</span>
              <Icon
                name={ICONS.RUN_FLAT_SERVICES}
                css={[styles.momentIcon, styles.runFlatIcon]}
              />
            </span>
          )}
          {installTomorrow?.label && (
            <span
              css={[
                styles.subcopy,
                styles.installTomorrow,
                isHighlightedDealAlertProduct && styles.subcopyBold,
              ]}
            >
              <Icon
                css={[styles.installIcon]}
                name={installTomorrow.icon.svgId}
              />
              <span>{installTomorrow.label}</span>
            </span>
          )}
          {installMonday?.label && (
            <span
              css={[
                styles.subcopy,
                styles.installTomorrow,
                isHighlightedDealAlertProduct && styles.subcopyBold,
              ]}
            >
              <Icon
                css={[styles.installIcon]}
                name={installMonday.icon.svgId}
              />
              <span>{installMonday.label}</span>
            </span>
          )}
          {!isRetailMember && isLoggedIn && partNumber && (
            <h3
              css={[
                styles.subcopy,
                isHighlightedDealAlertProduct && styles.subcopyBold,
              ]}
            >
              {`Part: # ${partNumber}`}
            </h3>
          )}
          {activeFilterValueList && activeFilterValueList.length > 0 && (
            <div css={styles.filterItemContainer}>
              {activeFilterValueList.map((filter) => (
                <div css={styles.filterItem} key={filter}>
                  {filter}
                </div>
              ))}
            </div>
          )}
          {isHighlighted && !isDealerTire && (
            <ul css={styles.momentList}>
              {deliveryInfo?.value && (
                <li css={styles.moment}>
                  <Icon
                    css={[styles.momentIcon, styles.deliveryIcon]}
                    name={ICONS.SHIPPING_TRUCK}
                  />
                  <span>{deliveryInfo.value}</span>
                </li>
              )}
              {dataMomentList &&
                dataMomentList.map((item) => (
                  <li css={styles.moment} key={item.label}>
                    <Icon css={styles.momentIcon} name={item.icon.svgId} />
                    <span>{item.label}</span>
                  </li>
                ))}
            </ul>
          )}
          <div css={styles.promosContainerMarginBottom}>
            {!isDealAlertProduct &&
              siteCatalogPromotionInfo?.list &&
              siteCatalogPromotionInfo.list.length > 0 && (
                <div css={styles.promosContainer}>
                  {siteCatalogPromotionInfo.list[0].icon && (
                    <Icon
                      css={styles.promosIcon}
                      name={
                        siteCatalogPromotionInfo.list[0].icon.svgId ===
                        ICONS.WRENCH
                          ? ICONS.TAG_FILL
                          : ICONS.FIRE
                      }
                    />
                  )}
                  <span css={styles.promos}>
                    {siteCatalogPromotionInfo.list.map((promotion, index) => {
                      const label =
                        index < siteCatalogPromotionInfo.list.length - 1
                          ? `${promotion.label},`
                          : promotion.label;
                      return promotion.label ? (
                        <span key={index}>{label}</span>
                      ) : null;
                    })}
                  </span>
                </div>
              )}
          </div>
        </div>
      </div>

      {(isSpecialOrder || isFleetOrder) && (
        <FleetModal
          content={''}
          image={
            isSpecialOrder
              ? { ...SPECIAL_IMAGE, height: 270, width: 480 }
              : { ...FLEET_IMAGE, height: 270, width: 480 }
          }
          subtitle={
            isSpecialOrder
              ? ui('cart.cartSummaryModal.specialOrder.subtitle')
              : ui('cart.cartSummaryModal.fleetModal.subtitle')
          }
          title={
            isSpecialOrder
              ? ui('account.specialPricingDefault')
              : ui('cart.cartSummaryModal.fleetModal.title')
          }
          isOpen={isOpenFleetModal}
          onClose={closeFleetModal}
        />
      )}
      <JoinSimpleCrewModal
        isOpen={openSimpleCrewModal}
        onClose={closeSimpleCrewModal}
        isSimpleCrewPrice
      ></JoinSimpleCrewModal>
      <PuncturedModal
        isOpen={isOpenPuncturedModal}
        onClose={togglePuncturedModal}
        openManufacturersWarrantiesModal={openManufacturersWarrantiesModal}
      />
      <ManufacturersWarrantiesModal
        isOpen={isManufacturersWarrantiesModalOpen}
        onClose={closeManufacturersWarrantiesModal}
        openRoadHazardModal={togglePuncturedModal}
      />
    </div>
  );
}

const MemoProductListing = memo(ProductListing, (prevProps, nextProps) =>
  isStrictEqual(prevProps, nextProps),
);

function ProductListingWrapper(
  props: Omit<Props, 'userType' | 'isFleet' | 'userDetail'>,
) {
  const { userType, isFleet, userDetail } =
    useUserPersonalizationContextSelector((v) => ({
      userType: v.userType,
      isFleet: v.isFleet,
      userDetail: v.userDetail,
    }));
  return (
    <MemoProductListing
      {...props}
      userType={userType}
      isFleet={isFleet}
      userDetail={userDetail}
    />
  );
}

export default ProductListingWrapper;
