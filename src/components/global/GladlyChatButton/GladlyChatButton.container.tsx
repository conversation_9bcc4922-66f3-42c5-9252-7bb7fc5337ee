'use client';

import { usePathname } from 'next/navigation';

import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { USER_TYPE } from '~/lib/constants/sso';
import { isOTSDeployment } from '~/lib/utils/deploy';

import GladlyChatButton from './GladlyChatButton';

function GladlyChatButtonContainer() {
  const pathname = usePathname();
  const isWalmartOrderPage = pathname === ROUTE_MAP[ROUTES.WALMART_ORDER_PAGE];
  const isDealerTireOrderPage = pathname === ROUTE_MAP[ROUTES.DT_EMPLOYEES];
  const { isDealerTire, isSpecialOrderUser } =
    useUserPersonalizationContextSelector((v) => ({
      isDealerTire: v.isDealerTire || isDealerTireOrderPage,
      isSpecialOrderUser: v.userType === USER_TYPE.SPECIAL_ORDER,
    }));

  const isOTS = isOTSDeployment();

  if (isWalmartOrderPage || isSpecialOrderUser || isDealerTire || isOTS) {
    return null;
  }
  return <GladlyChatButton />;
}
export default GladlyChatButtonContainer;
