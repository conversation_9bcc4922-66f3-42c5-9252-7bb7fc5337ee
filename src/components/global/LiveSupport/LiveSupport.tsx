import { usePathname, useSearchParams } from 'next/navigation';
import {
  KeyboardEvent as ReactKeyboardEvent,
  MouseEvent as ReactMouseEvent,
  useCallback,
  useState,
} from 'react';

import Image from '~/components/global/Image/Image';
import { useContactEmailModalContextSelector } from '~/context/ContactEmailModal.context';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useSiteGlobalsContextSelector } from '~/context/SiteGlobals.context';
import { useGladlyWidgetScriptContextSelector } from '~/context/ThirdPartyScriptsContext/AllGladlyScriptsContext/GladlyWidgetScript.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { useWidgetConfigContextSelector } from '~/context/WidgetConfig.context';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { openDealerTireChatWindow } from '~/lib/helpers/salesforce';
import { openGladlyChatWindow } from '~/lib/utils/gladly/gladly.utils';
import { openPhone } from '~/lib/utils/phone';
import { formatPhoneNumber } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';

import Grid from '../Grid/Grid';
import GridItem from '../Grid/GridItem';
import Icon from '../Icon/Icon';
import { ICONS } from '../Icon/Icon.constants';
import { formatHours } from '../InstallationShopDetails/InstallationShopDetails.utils';
import {
  ctaTextList,
  dealertireEmail,
  dealertirePhoneDisplay,
  dealertirePhoneNumber,
  email,
  pirelliEmail,
  pirelliPhoneNumber,
  simpleShopEmail,
  simpleShopPhoneNumber,
  smsNumber,
  supportPhoneNumber,
} from './LiveSupport.constants';
import styles from './LiveSupport.styles';
import { Props } from './LiveSupport.types';
import { CtaItem, SupportHeader } from './LiveSupportItem';
import { SMSCtaItem } from './LiveSupportSMSItem';

function CtaChat({ isOnLearnModal, isOnContactPage, isDealerTire }: Props) {
  const isGladlyWidgetLoaded = useGladlyWidgetScriptContextSelector(
    (v) => v.isGladlyWidgetLoaded,
  );

  return (
    <CtaItem
      item={{
        ...ctaTextList[0],
        action: () =>
          isDealerTire ? openDealerTireChatWindow() : openGladlyChatWindow({}),
        disabled: !isDealerTire && !isGladlyWidgetLoaded,
      }}
      index={0}
      isOnLearnModal={isOnLearnModal}
      isOnContactPage={isOnContactPage}
    />
  );
}

function CtaSMS({
  isOnLearnModal,
  isOnContactPage,
  isSimpleShop,
  isOTS,
}: Props) {
  const { isMobile, isTablet } = useSiteGlobalsContextSelector((v) => ({
    isMobile: v.isMobile,
    isTablet: v.isTablet,
  }));
  const isMobileOrTablet = (isMobile || isTablet) ?? false;
  const [showSMSTermsModal, setShowSMSTermsModal] = useState(false);

  const handleCloseTermsModal = (
    event: MouseEvent | ReactMouseEvent | ReactKeyboardEvent,
  ) => {
    // OPT-2276. try to avoid open SMS App alert is shown after close information modal.
    event.stopPropagation();
    setShowSMSTermsModal(false);
  };

  const learnMoreClickHandler = (
    event: MouseEvent | ReactMouseEvent | ReactKeyboardEvent,
  ) => {
    // OPT-2276. try to avoid open SMS App alert is shown after click learn more link
    event.stopPropagation();
    setShowSMSTermsModal(true);
  };

  const openSMS = () => {
    if (!showSMSTermsModal) {
      window.location.href = `sms:${smsNumber}`;
    }
  };

  return (
    <SMSCtaItem
      item={
        isMobileOrTablet
          ? {
              ...ctaTextList[1],
              action: () => openSMS(),
            }
          : {
              ...ctaTextList[1],
            }
      }
      index={1}
      isOnLearnModal={isOnLearnModal}
      isOnContactPage={isOnContactPage}
      learnMoreClickHandler={learnMoreClickHandler}
      showSMSTermsModal={showSMSTermsModal}
      handleCloseTermsModal={handleCloseTermsModal}
      isSimpleShop={isSimpleShop}
      isOTS={isOTS}
    />
  );
}

function CtaTelephone({
  isOnLearnModal,
  isOnContactPage,
  isSourcePirelli,
  isDealerTire,
  isSimpleShop,
}: Props) {
  const { isMobile, isTablet } = useSiteGlobalsContextSelector((v) => ({
    isMobile: v.isMobile,
    isTablet: v.isTablet,
  }));
  const isMobileOrTablet = (isMobile || isTablet) ?? false;

  const phoneToUse = isSimpleShop
    ? simpleShopPhoneNumber
    : isDealerTire
      ? dealertirePhoneNumber
      : isSourcePirelli
        ? pirelliPhoneNumber
        : supportPhoneNumber;
  const displayPhoneNumber =
    isSimpleShop || isSourcePirelli
      ? {
          ...ctaTextList[2],
          subtitle: formatPhoneNumber(phoneToUse, true) || '',
        }
      : isDealerTire
        ? { ...ctaTextList[2], subtitle: dealertirePhoneDisplay }
        : ctaTextList[2];

  const openTelephone = useCallback(() => {
    openPhone(phoneToUse);
  }, [phoneToUse]);

  return (
    <CtaItem
      item={
        isMobileOrTablet
          ? {
              ...displayPhoneNumber,
              action: () => openTelephone(),
            }
          : {
              ...displayPhoneNumber,
            }
      }
      index={2}
      isOnLearnModal={isOnLearnModal}
      isOnContactPage={isOnContactPage}
    />
  );
}

function CtaEmail({
  isOnLearnModal,
  isOnContactPage,
  isSourcePirelli,
  isDealerTire,
  isSimpleShop,
}: Props) {
  const toggleCustomerEmailModal = useContactEmailModalContextSelector(
    (v) => v.toggleCustomerEmailModal,
  );

  const emailToUse = isSimpleShop
    ? simpleShopEmail
    : isSourcePirelli
      ? pirelliEmail
      : isDealerTire
        ? dealertireEmail
        : email;

  const displayEmail =
    isSimpleShop || isSourcePirelli || isDealerTire
      ? { ...ctaTextList[3], subtitle: emailToUse }
      : ctaTextList[3];

  return (
    <CtaItem
      item={{
        ...displayEmail,
        action: isDealerTire
          ? () => {
              window.location.href =
                'mailto:<EMAIL>';
            }
          : () => toggleCustomerEmailModal(true),
      }}
      index={3}
      isOnLearnModal={isOnLearnModal}
      isOnContactPage={isOnContactPage}
    />
  );
}

function LiveSupport({
  isOnContactPage,
  isOnLearnModal,
  isOnRecommendationsModal,
}: Props) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const widgetSource = searchParams?.get('source')?.toLowerCase();
  const isSourcePirelli =
    widgetSource === 'www.pirelli.com' ||
    widgetSource === 'qlt-www.pirelli.com';

  const isDealerTireOrderPage = pathname === ROUTE_MAP[ROUTES.DT_EMPLOYEES];
  const isDealerTire = useUserPersonalizationContextSelector(
    (v) => v.isDealerTire || isDealerTireOrderPage,
  );

  const wrapElements = isOnContactPage || isOnLearnModal;
  const { isSimpleShop, isOTS } = useGlobalsContextSelector((v) => ({
    isSimpleShop: Number(v.isSimpleShop) === 1,
    isOTS: Number(v.isOTS) === 1,
  }));
  const widgetAppConfig = useWidgetConfigContextSelector(
    (v) => v.widgetAppConfig,
  );
  const installerPhone = widgetAppConfig?.phone;
  const installerAddress = widgetAppConfig
    ? `${widgetAppConfig.address1}, ${widgetAppConfig.city}, ${widgetAppConfig.state} ${widgetAppConfig.zip}`
    : '';
  const installerLogoUrl = widgetAppConfig?.logoUrl;
  const installerHours = widgetAppConfig?.hours;
  const hours = installerHours && formatHours(installerHours);
  const installerName = widgetAppConfig?.name;
  const hoursSplit = hours && hours.split(',').map((item) => item.trim());
  const isSimpleShopMobileInstaller = widgetAppConfig?.isMobileInstaller;
  const isMSSOTS = isSimpleShop || isOTS;

  return isOnContactPage ? (
    <Grid>
      <GridItem
        gridColumnL={'3/13'}
        gridColumnXL={'5/11'}
        css={styles.contactContainer}
      >
        <SupportHeader showEnableText isDealerTire={isDealerTire} />
        <div
          css={[styles.ctaList, wrapElements && styles.wrapElementsContactXL]}
        >
          <CtaChat
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isDealerTire={isDealerTire}
          />
          {!isSourcePirelli && !isDealerTire && (
            <CtaSMS
              isOnContactPage={isOnContactPage}
              isOnLearnModal={isOnLearnModal}
            />
          )}
          <CtaTelephone
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isSourcePirelli={isSourcePirelli}
            isDealerTire={isDealerTire}
          />
          <CtaEmail
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isSourcePirelli={isSourcePirelli}
            isDealerTire={isDealerTire}
          />
        </div>
      </GridItem>
    </Grid>
  ) : (
    <div css={isMSSOTS && styles.contactWrapper}>
      {isMSSOTS && (
        <div>
          <div
            css={[
              styles.headerContainer,
              isMSSOTS && styles.headerContainerUpdate,
            ]}
          >
            <h2 css={styles.header}>
              {installerName
                ? ui('support.installersEnabledCJ') + ' ' + installerName
                : ui('support.installersEnabledCJ')}
            </h2>
            <div
              css={[
                styles.ctaList,
                wrapElements && styles.wrapElementsContactXL,
                isMSSOTS && styles.ctaListUpdate,
              ]}
            >
              {isOTS && (
                <div css={styles.cta}>
                  <Icon
                    name={ICONS.PHONE}
                    css={[styles.iconOnFooter, isOTS && styles.iconOnFooterOTS]}
                  />
                  <div css={styles.ctaText}>
                    <p css={[styles.ctaTitle]}>Call</p>
                    <h2 css={[styles.ctaSubtitle, styles.ctaSubtitleLearn]}>
                      {installerPhone}
                    </h2>
                  </div>
                </div>
              )}
              {!isSimpleShopMobileInstaller && (
                <div css={styles.cta}>
                  <Icon
                    name={ICONS.LOCATION}
                    css={[
                      styles.iconOnFooter,
                      isSimpleShop && styles.iconOnFooterUpdate,
                      isOTS && styles.iconOnFooterOTS,
                    ]}
                  />
                  <div css={styles.ctaText}>
                    <p css={[styles.ctaTitle]}>Location</p>
                    <h2 css={[styles.ctaSubtitle, styles.ctaSubtitleLearn]}>
                      {installerAddress}
                    </h2>
                  </div>
                </div>
              )}
              {!isOTS && (
                <div css={styles.cta}>
                  <Icon
                    name={ICONS.PHONE}
                    css={[
                      styles.iconOnFooter,
                      isSimpleShop && styles.iconOnFooterUpdate,
                    ]}
                  />
                  <div css={styles.ctaText}>
                    <p css={[styles.ctaTitle]}>Call</p>
                    <h2 css={[styles.ctaSubtitle, styles.ctaSubtitleLearn]}>
                      {installerPhone}
                    </h2>
                  </div>
                </div>
              )}
              <div css={styles.cta}>
                <Icon
                  name={ICONS.CLOCK_WHITE}
                  css={[
                    styles.iconOnFooter,
                    isSimpleShop && styles.iconOnFooterUpdate,
                    isOTS && styles.iconOnFooterOTS,
                  ]}
                />
                <div css={styles.ctaText}>
                  <p css={[styles.ctaTitle]}>Store hours</p>
                  <h2 css={[styles.ctaSubtitle, styles.ctaSubtitleLearn]}>
                    {hoursSplit &&
                      hoursSplit.map(
                        (hour, index) =>
                          index > 0 && (
                            <span key={index + hour}>
                              {hour}
                              <br />
                            </span>
                          ),
                      )}
                    <span>{hoursSplit && hoursSplit[0]}</span>
                  </h2>
                </div>
              </div>
            </div>
            {installerLogoUrl && isSimpleShop && (
              <div>
                <Image
                  altText="guided shopping installer"
                  src={installerLogoUrl}
                  widths={[100, 180, 200]}
                  customImageStyles={[
                    styles.installerLogo,
                    isOTS ? styles.installerLogoOTS : {},
                  ]}
                />
              </div>
            )}
          </div>
        </div>
      )}
      {!isMSSOTS && (
        <div
          css={[
            isOnLearnModal ? styles.learnContainer : styles.container,
            isOnRecommendationsModal && styles.learnContainerUpdate,
            isSimpleShop && styles.containerUpdate,
          ]}
        >
          <SupportHeader
            showEnableText
            isOnRecommendationsModal={isOnRecommendationsModal}
            isDealerTire={isDealerTire}
          />
          <CtaChat
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isDealerTire={isDealerTire}
          />
          {!isSourcePirelli &&
            !isOnRecommendationsModal &&
            !isSimpleShop &&
            !isDealerTire && (
              <CtaSMS
                isOnContactPage={isOnContactPage}
                isOnLearnModal={isOnLearnModal}
              />
            )}
          <CtaTelephone
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isSourcePirelli={isSourcePirelli}
            isDealerTire={isDealerTire}
            isSimpleShop={isSimpleShop}
          />
          <CtaEmail
            isOnContactPage={isOnContactPage}
            isOnLearnModal={isOnLearnModal}
            isSourcePirelli={isSourcePirelli}
            isDealerTire={isDealerTire}
            isSimpleShop={isSimpleShop}
          />
        </div>
      )}
    </div>
  );
}
export default LiveSupport;
