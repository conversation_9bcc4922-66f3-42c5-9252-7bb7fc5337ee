import { MQ } from '~/lib/constants/breakpoints';
import { StylesMap } from '~/lib/constants/styles.types';

const styles: StylesMap = {
  desktopWrapper: {
    [MQ.L]: {
      display: 'flex',
      margin: '0 auto',
      minHeight: 530,
      width: 910,
    },
  },
  headerContent: {
    alignItems: 'center',
    display: 'flex',
    h1: {
      width: 'unset',
      textAlign: 'left',
    },
    justifyContent: 'center',
    width: '100%',
    [MQ.L]: {
      h1: {
        width: 450,
      },
      width: '50%',
    },
  },
  headerContentLeft: {},
  searchContainer: {
    '> div': {
      margin: '0px auto',
    },
    alignItems: 'center',
    display: 'flex',
    flexFlow: 'column',
    flexGrow: 1,
    justifyContent: 'center',
  },
};

export default styles;
