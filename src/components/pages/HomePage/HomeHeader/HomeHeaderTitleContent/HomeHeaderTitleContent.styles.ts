import { BREAKPOINTS, MQ } from '~/lib/constants/breakpoints';
import { COLORS } from '~/lib/constants/colors';
import { EASING } from '~/lib/constants/easing';
import { SPACING } from '~/lib/constants/spacing';
import { StylesMap } from '~/lib/constants/styles.types';
import { TIME } from '~/lib/constants/time';
import { typography } from '~/styles/typography.styles';

export const TITLE_DURATION = TIME.MS300;
export const TITLE_DELAY = {
  S: 1750,
  L: 1500,
};

export const styles: StylesMap = {
  container: {
    width: '100%',
  },
  darkMode: {
    backgroundColor: COLORS.GLOBAL.BLACK,
    color: COLORS.GLOBAL.WHITE,
  },
  description: [
    typography.largeCopy,
    {
      [MQ.L]: {
        transitionDelay: `${TITLE_DELAY.L}ms`,
      },
      paddingTop: SPACING.SIZE_05,
      textAlign: 'center',
      transition: `opacity ${TITLE_DURATION}ms ${EASING.CUBIC_EASE_OUT}`,
      transitionDelay: `${TITLE_DELAY.S}ms`,
    },
  ],
  subtitle: [
    typography.secondaryHeadline,
    {
      textTransform: 'uppercase',
      paddingBottom: SPACING.SIZE_15,
      textAlign: 'center',
    },
  ],
  title: [
    {
      [MQ.S]: {
        fontSize: 12,
        width: '90%',
      },
      [MQ.M]: {
        marginBottom: 0,
        width: BREAKPOINTS.M,
      },
      [MQ.L]: {
        br: {
          display: 'none',
        },
        fontSize: 24,
        height: 64,
        marginBottom: 0,
        transitionDelay: `${TITLE_DELAY.L}ms`,
        width: 920,
      },
      alignSelf: 'center',
      button: {
        borderBottom: '3px dotted',
        color: COLORS.GLOBAL.PRIMARY,
        marginTop: 0,
        textDecorationThickness: '2px',
        textTransform: 'uppercase',
        textUnderlineOffset: 5,
        [MQ.S]: {
          fontSize: 14,
        },
        [MQ.M]: {
          fontSize: 12,
        },
        [MQ.L]: {
          fontSize: 24,
          marginLeft: 5,
        },
      },
      color: COLORS.GLOBAL.WHITE,
      fontSize: 24,
      fontWeight: 700,
      marginBottom: SPACING.SIZE_10,
      marginLeft: 'auto',
      marginRight: 'auto',
      span: {
        color: COLORS.GLOBAL.WHITE,
        lineHeight: '14px',
        marginTop: 5,
        textDecorationThickness: '2px',
        textShadow: '0px 4px 20px rgba(0,0,0,0.7)',
        textUnderlineOffset: 5,
      },
      strong: {
        color: COLORS.GLOBAL.PRIMARY,
      },
      textAlign: 'center',
      textTransform: 'uppercase',
      transition: `opacity ${TITLE_DURATION}ms ${EASING.CUBIC_EASE_OUT}`,
      transitionDelay: `${TITLE_DELAY.S}ms`,
    },
  ],
  titleEPP: {
    span: {
      color: COLORS.GLOBAL.WHITE,
      textDecoration: 'none',
    },
    button: {
      span: {
        color: COLORS.GLOBAL.ORANGE,
      },
    },
  },
  titleUpdate: [
    typography.smallJumboHeadline,
    {
      textTransform: 'none',
      [MQ.S]: {
        fontSize: 30,
        marginBottom: SPACING.SIZE_40,
      },
      [MQ.M]: {
        fontSize: 40,
        marginBottom: SPACING.SIZE_30,
        paddingTop: SPACING.SIZE_30,
        lineHeight: '50px',
      },
      button: [
        typography.smallJumboHeadline,
        {
          [MQ.S]: {
            fontSize: 30,
          },
          [MQ.M]: {
            fontSize: 40,
          },
        },
      ],
    },
  ],
  titleUpdateDark: {
    span: {
      color: COLORS.GLOBAL.WHITE,
      textDecoration: 'underline',
      textDecorationThickness: 'from-font',
    },
  },
};
