import type {
  GooglePaymentTokenizePayload,
  HostedFieldsTokenizePayload,
  PayPalTokenizePayload,
} from 'braintree-web';
import lscache from 'lscache';
import { parseCookies, setCookie } from 'nookies';
import { useCallback, useEffect, useRef } from 'react';

import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { cookieConfig } from '~/components/modules/Cart/CartSummary.constants';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { isValidCartShipping } from '~/components/modules/Cart/CartSummaryModal/ShippingService/shippingService.utils';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import {
  ErrorContentType,
  isApplePayPayload,
  isFastlanePayload,
  isPayPalPayload,
  isResolvePayload,
  PaymentPayload,
  usePaymentDataContextSelector,
  VenmoPayload,
} from '~/components/modules/PaymentData/PaymentData.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import {
  OrderPaymentType,
  SiteCartOrderRequestWithoutSessionId,
} from '~/data/models/SiteCartOrderRequest';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import {
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { SiteInstallerItem } from '~/data/models/SiteInstallerItem';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import { usePreviousState } from '~/hooks/usePreviousState';
import useRouter from '~/hooks/useRouter';
import {
  apiCreateSiteCartBilling,
  apiUpdateSiteCartBilling,
} from '~/lib/api/checkout/cart-billing';
import { apiCreateSiteCartOrder } from '~/lib/api/checkout/cart-order';
import { apiUpdateCartShipping } from '~/lib/api/checkout/cart-shipping';
import { COOKIES } from '~/lib/constants/cookies';
import { FS_EVENT_NAMES } from '~/lib/constants/fullstory';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { USER_TYPE } from '~/lib/constants/sso';
import { TIME } from '~/lib/constants/time';
import { eventEmitters } from '~/lib/events/emitters';
import GA from '~/lib/helpers/analytics';
import { setFSCustomEvent } from '~/lib/helpers/fullstory';
import logger from '~/lib/helpers/logger';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { seStorage } from '~/lib/utils/browser-storage';
import { ui } from '~/lib/utils/ui-dictionary';

import {
  mapToInstallerCartShippingRequest,
  mapToShipToMeCartShippingRequest,
} from '../../checkout.util';
import { ShipToMeFormValues } from '../../Shipping/ShippingForNonInstallableTires/ShipToMe/ShipToMe.types';
import { VehicleInfo } from '../../ShippingConfirmation/ShippingConfirmation.types';
import { BillingInfoValues } from '../BillingInfoForm/BillingInfo.types';
import { useBraintreeContextSelector } from '../Braintree/Braintree.context';
import { PaymentPayloadWithBillingAddress } from '../GooglePayForm/GooglePay.utils';
import { VALID_FORM_EXCLUSIVE_OPTIONS } from '../PaymentActionBar/SubmitPaymentButton';
import { mapBillingInfoToBillingRequest } from '../SimplePay/SimplePay.utils';
import { usePaymentContextSelector } from './Payment.context';

interface PaymentWithBraintreeEffectProps {
  initialBillingInfo: Partial<BillingInfoValues>;
  paymentType: PAYMENT_OPTIONS;
  siteShipping?: SiteCartShippingResponse | null;
  userIp: string | null | undefined;
}

export default function PaymentEffectForMainSite({
  initialBillingInfo,
  paymentType,
  siteShipping,
  userIp,
}: PaymentWithBraintreeEffectProps) {
  const router = useRouter();
  const {
    billingInfo,
    cartShippingInfo,
    isContinueButtonEnabled,
    isLoading,
    isUserInfoFormValid,
    paymentPayload,
    setIsContinueButtonEnabled,
    setIsLoading,
    setIsSaveBillingSuccess,
    setPaymentPayload,
    submitTracker,
    updatePaymentError,
    updatePaymentErrorTitle,
    userInfoFormValues,
  } = usePaymentContextSelector((v) => ({
    billingInfo: v.billingInfo,
    cartShippingInfo: v.cartShippingInfo,
    isContinueButtonEnabled: v.isContinueButtonEnabled,
    isLoading: v.isLoading,
    isUserInfoFormValid: v.isUserInfoFormValid,
    paymentPayload: v.paymentPayload,
    setIsContinueButtonEnabled: v.setIsContinueButtonEnabled,
    setIsLoading: v.setIsLoading,
    setIsSaveBillingSuccess: v.setIsSaveBillingSuccess,
    setPaymentPayload: v.setPaymentPayload,
    submitTracker: v.submitTracker,
    updatePaymentError: v.updatePaymentError,
    updatePaymentErrorTitle: v.updatePaymentErrorTitle,
    userInfoFormValues: v.userInfoFormValues,
  }));
  const { setCrossPageErrorMessage, setPaymentData } =
    usePaymentDataContextSelector((v) => ({
      setCrossPageErrorMessage: v.setCrossPageErrorMessage,
      setPaymentData: v.setPaymentData,
    }));

  const { getUserDetail, isDealerTire } = useUserPersonalizationContextSelector(
    (v) => ({
      isDealerTire: v.isDealerTire,
      getUserDetail: v.getUserDetail,
    }),
  );
  const {
    cartSummary,
    setCustomerId,
    setIsCartSummaryModalOpen,
    setIsOpenTimeChangeModalOnCheckout,
    updateCartSummary,
  } = useCartSummaryContextSelector((v) => ({
    cartSummary: v.cartSummary,
    setCustomerId: v.setCustomerId,
    setIsCartSummaryModalOpen: v.setIsCartSummaryModalOpen,
    setIsOpenTimeChangeModalOnCheckout: v.setIsOpenTimeChangeModalOnCheckout,
    updateCartSummary: v.updateCartSummary,
  }));
  const {
    hasValidShippingPlace,
    activeOption,
    isShipToMeTabSelected,
    setIsShippingFormOpen,
    setIsShopCardError,
  } = useCartUserActionContextSelector((v) => ({
    activeOption: v.activeOption,
    hasValidShippingPlace: v.hasValidShippingPlace,
    isShipToMeTabSelected: v.isShipToMeTabSelected,
    setIsShippingFormOpen: v.setIsShippingFormOpen,
    setIsShopCardError: v.setIsShopCardError,
  }));
  const {
    cartAppointment,
    cartShipping,
    updateCartShipping,
    createCartShipping,
  } = useCartShippingContextSelector((v) => ({
    cartAppointment: v.cartAppointment,
    cartShipping: v.cartShipping,
    createCartShipping: v.createCartShipping,
    updateCartShipping: v.updateCartShipping,
  }));
  const getDeviceData = useBraintreeContextSelector((v) => v.getDeviceData);

  //check if user is navigating from retrieve quote
  const isRetrieveQuote =
    lscache.get(LOCAL_STORAGE[PROPERTIES.RETRIEVE_QUOTE]) === 'yes';
  const localVehicleData: VehicleInfo = lscache.get(
    LOCAL_STORAGE[PROPERTIES.VEHICLE_METADATA],
  );
  const {
    vehicleMake = '',
    vehicleModel = '',
    vehicleTrim = '',
    vehicleYear = '',
  } = localVehicleData ?? {};
  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] ?? '';
  const forterToken = cookies[COOKIES.FORTER] || null;

  const previousSubmitTracker = usePreviousState(submitTracker);
  const placeOrderInitiated = useRef(false);

  const disablePlaceOrderOn539 = useCallback(() => {
    const date = new Date();
    date.setTime(date.getTime() + 60 * 60 * 1000);
    setCookie(null, COOKIES.DISABLE_PLACE_ORDER, '1', {
      path: '/',
      secure: false,
      domain: COOKIES.DOMAIN,
      expires: date,
    });
  }, []);

  const upsertCartShipping = useCallback(async () => {
    if (
      !cartShippingInfo ||
      (!cartShippingInfo.email &&
        cartShipping?.shippingOption !== ShippingType.INSTALLER)
    ) {
      return;
    }

    const upsertCartShippingInfo = cartSummary?.cartShippingAddressPresent
      ? updateCartShipping
      : createCartShipping;

    if (
      isShipToMeTabSelected &&
      activeOption === ShippingType.HOME &&
      (!cartShipping ||
        !isValidCartShipping(cartShipping.cartShipping) ||
        (!isRetrieveQuote && !cartShippingInfo.phone))
    ) {
      await upsertCartShippingInfo({
        ...cartShippingInfo,
        phone: userInfoFormValues.phone,
      });
      return;
    }

    if (
      cartShipping &&
      cartShipping?.shippingOption === ShippingType.INSTALLER &&
      cartShipping.cartShipping.installer
    ) {
      const { installer, phoneType, firstName, lastName } =
        cartShipping.cartShipping;
      await updateCartShipping({
        addressLine1: installer.addressLine1,
        addressLine2: installer.addressLine2,
        city: installer.city,
        email: userInfoFormValues.email || cartShippingInfo.email,
        firstName,
        installerId: installer.installerId,
        lastName,
        phone: installer.phone,
        phoneType,
        shippingOption: ShippingType.INSTALLER,
        state: installer.state,
        vehicleMake,
        vehicleModel,
        vehicleTrim,
        vehicleYear,
        zip: installer.zip,
      });
    }
  }, [
    activeOption,
    cartShipping,
    cartShippingInfo,
    cartSummary?.cartShippingAddressPresent,
    createCartShipping,
    isShipToMeTabSelected,
    updateCartShipping,
    userInfoFormValues.email,
    userInfoFormValues.phone,
    vehicleMake,
    vehicleModel,
    vehicleTrim,
    vehicleYear,
    isRetrieveQuote,
  ]);

  const checkShippingInfoHasErrors = useCallback((): boolean => {
    if (hasValidShippingPlace) {
      return false;
    }

    eventEmitters.setShopCardError.emit(true);
    return true;
  }, [hasValidShippingPlace]);

  const upsertCartSummary = useCallback(async () => {
    let requestBody: SiteCartSummaryRequest = {};

    if (
      cartSummary?.siteCartVehicle &&
      !(cartShippingInfo?.email || userInfoFormValues.email)
    ) {
      return;
    }

    if (!cartSummary?.siteCartVehicle) {
      requestBody = {
        ...requestBody,
        vehicleMake,
        vehicleModel,
        vehicleTrim,
        vehicleYear,
      };
    }

    if (cartShippingInfo?.email || userInfoFormValues.email) {
      requestBody = {
        ...requestBody,
        email: cartShippingInfo?.email || userInfoFormValues.email,
      };
    }

    await updateCartSummary(requestBody);
  }, [
    cartShippingInfo?.email,
    updateCartSummary,
    userInfoFormValues.email,
    vehicleMake,
    vehicleModel,
    vehicleTrim,
    vehicleYear,
    cartSummary?.siteCartVehicle,
  ]);

  const placeOrder = useCallback(
    async ({
      payload,
      paymentType,
      siteBilling,
    }: {
      payload: PaymentPayload;
      paymentType: PAYMENT_OPTIONS;
      siteBilling: SiteCartBillingResponse;
    }) => {
      let input: SiteCartOrderRequestWithoutSessionId;
      if (isResolvePayload(payload)) {
        input = {
          cartId: storedCartId,
          paymentType: OrderPaymentType.RESOLVE,
          resolve: {
            chargeId: payload.chargeId,
          },
          userAgent: window.navigator.userAgent,
          userIp,
        };
      } else {
        const deviceData =
          paymentType === PAYMENT_OPTIONS.VENMO
            ? (payload as VenmoPayload).deviceData
            : getDeviceData();
        const isFastlane = isFastlanePayload(payload);
        input = {
          braintree: {
            cardType: paymentType,
            paymentMethodNonce: isFastlane ? payload.id : payload.nonce,
            last4Digits: null,
            deviceData,
          },
          cartId: storedCartId,
          forterToken,
          paymentType: OrderPaymentType.BRAINTREE,
          source:
            seStorage.getItem(SESSION_STORAGE[SESSION_PROPERTIES.SOURCE]) ??
            undefined,
          subSource:
            seStorage.getItem(SESSION_STORAGE[SESSION_PROPERTIES.SUB_SOURCE]) ??
            undefined,
          userAgent: window.navigator.userAgent,
          userIp,
          widgetSource:
            seStorage.getItem(
              SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
            ) ?? undefined,
        };
        if (paymentType === PAYMENT_OPTIONS.CREDIT) {
          input.braintree.last4Digits = isFastlane
            ? payload.paymentSource.card.lastDigits
            : (payload as HostedFieldsTokenizePayload).details.lastFour;
        }
        if (paymentType === PAYMENT_OPTIONS.GOOGLE_PAY) {
          input.braintree.last4Digits = (
            payload as GooglePaymentTokenizePayload
          ).details.lastFour;
        }
      }

      try {
        // have to make sure ssoUid appended because it is cleared after redirected from 3rd party payment
        const userDetailResponse = await getUserDetail();
        const userType =
          userDetailResponse?.accountTypes?.[0]?.name ?? USER_TYPE.NONE;

        const response = await apiCreateSiteCartOrder({
          input,
        });
        if (response.isSuccess) {
          setPaymentData({ paymentType, payload });
          GA.addToDataLayer({
            cartSummary,
            customerId: response.data.customerId,
            emailMatchesExistingAccount:
              response.data.emailMatchesExistingAccount,
            event: 'isCheckoutComplete',
            isAdmin: response.data.isAdmin,
            orderId: response.data.order.orderNo,
            orderSource: response.data.orderSource,
            orderStats: response.data.orderStats,
            siteBilling,
            siteCartOrderResponse: response.data.order,
            siteShipping,
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_COMPLETED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              customer_id: response.data.customerId ?? '',
              order_id: response.data.order.orderNo,
              payment_method: paymentType ?? '',
              shipping_method: cartShipping?.shippingOption ?? '',
            },
          );
          setCookie(
            null,
            COOKIES.ORDER_ID,
            response.data.order.orderNo + '',
            cookieConfig,
          );
          setCustomerId(response.data.customer.id);
          setFSCustomEvent(FS_EVENT_NAMES.PLACE_ORDER, {
            accountType: userType,
            poNumber: siteBilling?.cartBilling.poNumber || '',
            referenceId: isResolvePayload(payload)
              ? payload.chargeId
              : undefined,
          });
          setFSCustomEvent(FS_EVENT_NAMES.ORDER_NUMBER, {
            orderNumber: String(response.data.order.orderNo),
          });

          setIsCartSummaryModalOpen(false);
          router.replace(
            ROUTE_MAP[ROUTES.ORDER_CONFIRMATION] +
              '/' +
              response.data.order.orderId,
          );
        } else {
          setPaymentPayload(undefined);
          // can't show real error message from service api because it contains db operation code in error message.
          // if (!response.isSuccess && response.error) {
          //   updatePaymentError(response.error);
          // } else {
          //   updatePaymentError({
          //     message: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
          //   });
          // }
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.PAYMENT_DECLINED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              payment_method: paymentType,
              shipping_method: cartShipping?.shippingOption ?? '',
              reason: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
            },
          );
          if (response.error.statusCode === 539) {
            disablePlaceOrderOn539();
            updatePaymentErrorTitle(
              ui('checkout.orderConfirm.payment539ErrorTitle'),
            );
            updatePaymentError({
              message: ui('checkout.orderConfirm.payment539ErrorMessage'),
            });
            setIsContinueButtonEnabled((prev) => ({
              ...prev,
              [paymentType]: true,
            }));

            window.scroll({
              top: 0,
              behavior: 'smooth',
            });
          } else if (
            response.error.statusCode === 400 &&
            response.error.message === 'Duplicated order error'
          ) {
            logger.warn('Tolerable duplication error');
          } else {
            updatePaymentErrorTitle(ui('checkout.paymentType.error.payment'));
            updatePaymentError({
              message: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
            });
            setIsContinueButtonEnabled((prev) => ({
              ...prev,
              [paymentType]: true,
            }));
          }
        }
      } catch (error) {
        logger.error('PaymentContext catch error: ', error);
        updatePaymentErrorTitle(ui('checkout.paymentType.error.payment'));
        updatePaymentError({
          message: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
        });
        setIsContinueButtonEnabled((prev) => ({
          ...prev,
          [paymentType]: true,
        }));
      } finally {
        setIsLoading(false);
      }
    },
    [
      storedCartId,
      userIp,
      getDeviceData,
      forterToken,
      getUserDetail,
      setPaymentData,
      cartSummary,
      siteShipping,
      cartShipping?.shippingOption,
      setCustomerId,
      setIsCartSummaryModalOpen,
      router,
      setPaymentPayload,
      disablePlaceOrderOn539,
      updatePaymentErrorTitle,
      updatePaymentError,
      setIsContinueButtonEnabled,
      setIsLoading,
    ],
  );

  const createOrUpdateCartBilling = useCallback(async () => {
    let input = mapBillingInfoToBillingRequest(billingInfo, userInfoFormValues);
    const isPayPal =
      isPayPalPayload(paymentPayload) && paymentType === PAYMENT_OPTIONS.PAYPAL;
    if (
      cartShipping &&
      cartShipping.shippingOption === ShippingType.HOME &&
      !isPayPal &&
      !isValidCartShipping(cartShipping.cartShipping)
    ) {
      setCrossPageErrorMessage({
        subtitle: 'Please enter your shipping details',
        title: 'Shipping details required',
        type: ErrorContentType.BILLING_INFO,
      });
      setIsShippingFormOpen(true);
      setIsLoading(false);
      return;
    }

    if (isPayPal) {
      const paypalDetails = paymentPayload.details;
      let email = paypalDetails.email;
      if (isDealerTire) {
        email = userInfoFormValues.email;
      }
      const updateCartShipping = async (
        cartShippingRequest: SiteCartShippingRequest,
      ) => {
        await apiUpdateCartShipping({
          input: cartShippingRequest,
          query: { cartId: storedCartId },
          includeUserRegion: true,
          includeUserZip: true,
        });
      };
      if (cartShipping && cartShipping.shippingOption === ShippingType.HOME) {
        const shiptoMeValuesWithAddress: ShipToMeFormValues = {
          addressLine1:
            cartShipping.cartShipping.addressLine1 ??
            paypalDetails.shippingAddress.line1,
          addressLine2:
            cartShipping.cartShipping.addressLine2 ??
            paypalDetails.shippingAddress.line2,
          city:
            cartShipping.cartShipping.city ??
            paypalDetails.shippingAddress.city,
          companyName: '',
          email: paypalDetails.email ?? cartShipping.cartShipping.email,
          firstName:
            cartShipping.cartShipping.firstName ?? paypalDetails.firstName,
          lastName:
            cartShipping.cartShipping.lastName ?? paypalDetails.lastName,
          phone: userInfoFormValues.phone ?? paypalDetails.phone,
          phoneType: PHONE_TYPE.MOBILE,
          state:
            cartShipping.cartShipping.state ??
            paypalDetails.shippingAddress.state,
          zip:
            cartShipping.cartShipping.zip ??
            paypalDetails.shippingAddress.postalCode,
        };
        const cartShippingRequest: SiteCartShippingRequest =
          mapToShipToMeCartShippingRequest(shiptoMeValuesWithAddress);
        await updateCartShipping(cartShippingRequest);
      } else if (
        cartShipping &&
        cartShipping.shippingOption === ShippingType.INSTALLER
      ) {
        if (paypalDetails) {
          const cartShippingRequest: SiteCartShippingRequest =
            mapToInstallerCartShippingRequest(
              cartSummary?.installerDetails as SiteInstallerItem,
              {
                email: paypalDetails.email ?? (cartSummary?.email as string),
                phone: paypalDetails.phone,
                vehicleDescription: '',
                vehicleMake: vehicleMake || '',
                vehicleModel: vehicleModel || '',
                vehicleTrim: vehicleTrim || '',
                vehicleYear: vehicleYear || '',
              },
            );
          await updateCartShipping(cartShippingRequest);
        }
      }
      await updateCartSummary({
        email,
      });
    }

    if (isPayPal) {
      const {
        details: {
          phone,
          billingAddress: { line1, line2, city, postalCode, state },
        },
      } = paymentPayload as PayPalTokenizePayload;
      input = {
        ...input,
        addressLine1: line1,
        addressLine2: line2,
        city,
        phone: userInfoFormValues.phone ?? phone,
        state,
        zip: postalCode,
      };
    }

    if (paymentType === PAYMENT_OPTIONS.GOOGLE_PAY && paymentPayload) {
      const {
        billingAddress: { addressLine1, addressLine2, city, zip, state },
      } = paymentPayload as PaymentPayloadWithBillingAddress;

      input = {
        ...input,
        addressLine1,
        addressLine2,
        city,
        state,
        zip,
      };
    }

    const cartApiFunc = initialBillingInfo.firstName
      ? apiUpdateSiteCartBilling
      : apiCreateSiteCartBilling;

    setCookie(null, COOKIES.BILLING_PAYMENT, '1', cookieConfig);

    const response = await cartApiFunc({
      input,
      query: { cartId: storedCartId },
    });

    if (response.isSuccess && paymentPayload) {
      setIsSaveBillingSuccess(true);
      if (
        isResolvePayload(paymentPayload) &&
        paymentPayload.chargeId.length === 0
      ) {
        return;
      }

      await placeOrder({
        payload: paymentPayload,
        paymentType,
        siteBilling: response.data.siteCartBillingResponse,
      });
    } else {
      // can't show real error message from service api because it contains db operation code in error message.
      // if (!response.isSuccess && response.error) {
      //   updatePaymentError(response.error);
      // } else {
      //   updatePaymentError({
      //     message: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
      //   });
      // }
      updatePaymentError({
        message: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
      });
      window.scroll({
        top: 0,
        behavior: 'smooth',
      });
      setIsLoading(false);
    }
    placeOrderInitiated.current = true;

    setTimeout(() => {
      placeOrderInitiated.current = false;
    }, TIME.MS3000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    cartShipping,
    updateCartSummary,
    billingInfo?.addressLine1,
    billingInfo?.addressLine2,
    billingInfo?.city,
    billingInfo?.firstName,
    billingInfo?.lastName,
    billingInfo?.phone,
    billingInfo?.phoneType,
    billingInfo?.poNumber,
    billingInfo?.state,
    billingInfo?.zip,
    billingInfo?.email,
    storedCartId,
    initialBillingInfo.firstName,
    paymentPayload,
    setCrossPageErrorMessage,
    setIsShippingFormOpen,
    placeOrder,
    paymentType,
    cartShippingInfo.email,
  ]);

  // when clicking submit button, attached click event has been dispatched and paymentPayload is set, this hooks will work.
  useEffect(() => {
    if (
      (!submitTracker ||
        (submitTracker === previousSubmitTracker &&
          !isApplePayPayload(paymentPayload)) || // to make sure it happens only when clicks button except apple pay
        checkShippingInfoHasErrors()) &&
      !(
        isResolvePayload(paymentPayload) && paymentPayload.chargeId.length !== 0
      )
    ) {
      return;
    }

    if (
      !paymentPayload ||
      !isContinueButtonEnabled[paymentType] ||
      isLoading ||
      placeOrderInitiated.current ||
      (!VALID_FORM_EXCLUSIVE_OPTIONS.includes(paymentType) &&
        !isUserInfoFormValid)
    ) {
      return;
    }

    if (cartAppointment === null) {
      setIsOpenTimeChangeModalOnCheckout(true);
      return;
    }

    if (
      cartShipping?.shippingOption === ShippingType.INSTALLER &&
      (!cartShipping || !cartAppointment)
    ) {
      setIsShopCardError(true);
    }

    setIsLoading(true);

    (async () => {
      await upsertCartSummary();
      await upsertCartShipping();
      await createOrUpdateCartBilling();
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    cartAppointment,
    paymentPayload,
    createOrUpdateCartBilling,
    isContinueButtonEnabled,
    isLoading,
    setIsOpenTimeChangeModalOnCheckout,
    paymentType,
    isUserInfoFormValid,
    submitTracker,
    upsertCartShipping,
    upsertCartSummary,
    cartShippingInfo.email,
  ]);

  return null;
}
