import type { BraintreeError } from 'braintree-web';
import lscache from 'lscache';
import dynamic from 'next/dynamic';
import { useSearchParams } from 'next/navigation';
import { parseCookies } from 'nookies';
import {
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from 'react';

import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import {
  ErrorContentType,
  PaymentPayload,
  usePaymentDataContextSelector,
} from '~/components/modules/PaymentData/PaymentData.context';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import {
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import useEffectOnlyOnce from '~/hooks/useEffectOnlyOnce';
import useRouter from '~/hooks/useRouter';
import { COOKIES } from '~/lib/constants/cookies';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { rudderstackSendIdentifyEvent } from '~/lib/helpers/rudderstack';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';
import { ui } from '~/lib/utils/ui-dictionary';

import {
  USER_INFO_FORM_FIELDS,
  UserInfoFormValues,
} from '../../Payments/UserInfoForm/UserInfoForm.types';
import { VehicleInfo } from '../../ShippingConfirmation/ShippingConfirmation.types';
import { BillingInfoValues } from '../BillingInfoForm/BillingInfo.types';

const PaymentEffectForMainSite = dynamic(
  () => import('./PaymentEffectForMainSite'),
);

interface PaymentProviderProps {
  children: ReactNode;
  initialBillingInfo: Partial<BillingInfoValues>;
  paymentType: PAYMENT_OPTIONS;
  siteShipping?: SiteCartShippingResponse | null;
  userIp?: string | null;
}

export type PaymentErrorType =
  | BraintreeError
  | Record<string, unknown>
  | undefined;

export function isPaymentErrorType(err: unknown): err is PaymentErrorType {
  return (typeof err === 'object' && err && 'message' in err) ?? false;
}

export interface PaymentContextProps {
  billingInfo: Partial<BillingInfoValues>;
  cartShippingInfo: SiteCartShippingRequest;
  handleClickBack: () => void;
  isAffirmDisabled: boolean;
  isContinueButtonEnabled: Record<string, boolean>;
  isKatapultDisabled: boolean;
  isKlarnaDisabled: boolean;
  isLoading: boolean;
  isPaypalLoaderActive: boolean;
  isPlaceOrderDisabledDueTo539: boolean;
  isSaveBillingSuccess: boolean;
  isSynchronyDisabled: boolean;
  isSynchronyPayment: boolean;
  isUserInfoFormValid: boolean;
  paymentError: PaymentErrorType;
  paymentErrorTitle?: string;
  paymentPayload?: PaymentPayload;
  paypalInfoModalOpen: boolean;
  selectedPaymentMethod: PAYMENT_OPTIONS;
  setBillingInfo: (billingInfo: Partial<BillingInfoValues>) => void;
  setCartShippingInfo: (value: SiteCartShippingRequest) => void;
  setIsAffirmDisabled: (value: boolean) => void;
  setIsContinueButtonEnabled: Dispatch<SetStateAction<Record<string, boolean>>>;
  setIsKatapultDisabled: (value: boolean) => void;
  setIsKlarnaDisabled: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
  setIsPaypalLoaderActive: (value: boolean) => void;
  setIsSaveBillingSuccess: (value: boolean) => void;
  setIsSynchronyDisabled: (value: boolean) => void;
  setIsSynchronyPayment: (value: boolean) => void;
  setIsUserInfoFormValid: (value: boolean) => void;
  setPaymentError: (error: PaymentErrorType) => void;
  setPaymentPayload: (payload: PaymentPayload | undefined) => void;
  setPaypalInfoModalOpen: (value: boolean) => void;
  setSubmitTracker: Dispatch<SetStateAction<number>>;
  setUserInfoFormValues: Dispatch<SetStateAction<UserInfoFormValues>>;
  submitTracker: number;
  updatePaymentError: (error: PaymentErrorType) => void;
  updatePaymentErrorTitle: (title: string) => void;
  userInfoFormValues: UserInfoFormValues;
}

const CANCEL_BY_USER_MESSAGE_MAP: Record<string, boolean> = {
  'PayPal payment canceled': true,
  'User canceled Venmo authorization by closing the Venmo Desktop modal.': true,
  'User closed the Payment Request UI.': true,
  'ApplePay payment canceled': true,
};

const PaymentContext = createContext<PaymentContextProps>();

const initialButtonStates = {
  [PAYMENT_OPTIONS.CREDIT]: false,
  [PAYMENT_OPTIONS.APPLE_PAY]: true,
  [PAYMENT_OPTIONS.GOOGLE_PAY]: false,
  [PAYMENT_OPTIONS.VENMO]: false,
  [PAYMENT_OPTIONS.AFFIRM]: false,
  [PAYMENT_OPTIONS.PAYPAL]: true,
  [PAYMENT_OPTIONS.SYNCHRONY]: false,
};

function useContextSetup({
  paymentType,
  initialBillingInfo,
  siteShipping,
}: Omit<PaymentProviderProps, 'children' | 'userIp'>): PaymentContextProps {
  const router = useRouter();
  const searchParams = useSearchParams();
  const charge_id = searchParams?.get('charge_id');
  //this value will be used to be notified when submit button is clicked
  const [submitTracker, setSubmitTracker] = useState<number>(0);
  const [isContinueButtonEnabled, setIsContinueButtonEnabled] =
    useState<Record<string, boolean>>(initialButtonStates);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaveBillingSuccess, setIsSaveBillingSuccess] = useState(false);

  const [billingInfo, setBillingInfo] =
    useState<Partial<BillingInfoValues>>(initialBillingInfo);
  const [paymentErrorTitle, updatePaymentErrorTitle] = useState<string>();
  const [paymentError, updatePaymentError] = useState<PaymentErrorType>();
  const [paymentPayload, setPaymentPayload] = useState<PaymentPayload>();
  const [paypalInfoModalOpen, setPaypalInfoModalOpen] = useState(false);

  const setCrossPageErrorMessage = usePaymentDataContextSelector(
    (v) => v.setCrossPageErrorMessage,
  );
  const setPaymentType = useCartSummaryContextSelector((v) => v.setPaymentType);
  const currentTab = useCartUserActionContextSelector((v) => v.currentTab);
  const { cartInstallLocation, cartShipping } = useCartShippingContextSelector(
    (v) => ({
      cartInstallLocation: v.cartInstallLocation,
      cartShipping: v.cartShipping,
    }),
  );

  const [isUserInfoFormValid, setIsUserInfoFormValid] =
    useState<boolean>(false);
  const [isAffirmDisabled, setIsAffirmDisabled] = useState(true);
  const [isKatapultDisabled, setIsKatapultDisabled] = useState(true);
  const initialUserInfoFormValues: UserInfoFormValues = {
    [USER_INFO_FORM_FIELDS.EMAIL]: siteShipping?.cartShipping.email || '',
    [USER_INFO_FORM_FIELDS.PHONE]:
      siteShipping?.cartShipping.phone || cartInstallLocation?.phone || '',
    [USER_INFO_FORM_FIELDS.PHONE_TYPE]:
      siteShipping?.cartShipping.phoneType || PHONE_TYPE.MOBILE,
  };
  const initialCartShippingInfo: SiteCartShippingRequest = {
    addressLine1: siteShipping?.cartShipping.addressLine1 || '',
    addressLine2: siteShipping?.cartShipping.addressLine2 || '',
    city: siteShipping?.cartShipping.city || '',
    companyName: siteShipping?.cartShipping.companyName || '',
    email: siteShipping?.cartShipping?.email || '',
    firstName: siteShipping?.cartShipping?.firstName || '',
    installerId: siteShipping?.cartShipping?.installer?.installerId || null,
    lastName: siteShipping?.cartShipping?.lastName || '',
    phone: siteShipping?.cartShipping.phone || '',
    phoneType: siteShipping?.cartShipping.phoneType || PHONE_TYPE.MOBILE,
    shippingOption: siteShipping?.shippingOption || ShippingType.HOME,
    state: siteShipping?.cartShipping.state || '',
    zip: siteShipping?.cartShipping.zip || '',
  };
  const localVehicleData: VehicleInfo = lscache.get(
    LOCAL_STORAGE[PROPERTIES.VEHICLE_METADATA],
  );

  const {
    vehicleMake = '',
    vehicleModel = '',
    vehicleTrim = '',
    vehicleYear = '',
  } = localVehicleData ?? {};
  const vehicleInformation = `${vehicleYear} ${vehicleMake} ${vehicleModel} ${vehicleTrim}`;
  const [userInfoFormValues, setUserInfoFormValues] =
    useState<UserInfoFormValues>({
      [USER_INFO_FORM_FIELDS.VEHICLE]: vehicleInformation || '',
      ...initialUserInfoFormValues,
    });
  const [isSynchronyDisabled, setIsSynchronyDisabled] = useState(true);
  const [isSynchronyPayment, setIsSynchronyPayment] = useState<boolean>(false);
  const [isKlarnaDisabled, setIsKlarnaDisabled] = useState(true);
  const [isPaypalLoaderActive, setIsPaypalLoaderActive] = useState(false);
  const [cartShippingInfo, setCartShippingInfo] =
    useState<SiteCartShippingRequest>(initialCartShippingInfo);
  const cookies = parseCookies();
  const isPlaceOrderDisabledDueTo539 = cookies[COOKIES.DISABLE_PLACE_ORDER]
    ? true
    : false;

  const handleClickBack = useCallback(() => {
    router.replace(ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]);
  }, [router]);

  const setPaymentError = useCallback(
    (error: PaymentErrorType) => {
      if (
        error &&
        typeof error.message === 'string' &&
        CANCEL_BY_USER_MESSAGE_MAP[error.message]
      ) {
        setCrossPageErrorMessage({
          subtitle: error.message,
          title: ui('checkout.paymentType.error.payment'),
          type: ErrorContentType.BILLING_INFO,
        });
      } else {
        updatePaymentError(error);
      }
    },
    [setCrossPageErrorMessage],
  );

  useEffect(() => {
    if (cartShipping && cartShipping.cartShipping) {
      const shippingData = cartShipping.cartShipping;
      const cartShippingInfo: SiteCartShippingRequest = {
        addressLine1: shippingData.addressLine1,
        addressLine2: shippingData.addressLine2,
        city: shippingData.city,
        companyName: shippingData.companyName || '',
        email: shippingData.email,
        firstName: shippingData.firstName,
        installerId: shippingData.installer?.installerId || null,
        lastName: shippingData.lastName,
        phone: shippingData.phone || '',
        phoneType: shippingData.phoneType || PHONE_TYPE.MOBILE,
        shippingOption: cartShipping.shippingOption || ShippingType.HOME,
        state: shippingData.state,
        zip: shippingData.zip,
      };
      setCartShippingInfo(cartShippingInfo);
    }
  }, [cartShipping]);

  useEffect(() => {
    setSubmitTracker(0);
  }, [currentTab]);

  useEffectOnlyOnce(
    () => {
      const userSessionId = lscache.get(LOCAL_STORAGE[PROPERTIES.SESSION]);
      const traitData = {
        first_name: billingInfo?.firstName ?? '',
        last_name: billingInfo?.lastName ?? '',
        email: userInfoFormValues?.email ?? '',
        phone: userInfoFormValues?.phone ?? '',
      };
      rudderstackSendIdentifyEvent(userSessionId, traitData);
    },
    { billingInfo },
    ({ billingInfo }) => !!billingInfo,
  );

  // Shifting the logic of setting the payment payload to charge_id because when we we get back on ST page from Resolve Payment Screen we land on credit card form and not on resolve form that's why the hook didn't hit there.
  useEffect(() => {
    if (charge_id) {
      setPaymentType(PAYMENT_OPTIONS.RESOLVE);
      setPaymentPayload({
        chargeId: charge_id as string,
      });
    }
  }, [setPaymentPayload, setPaymentType, charge_id]);

  return {
    billingInfo,
    cartShippingInfo,
    handleClickBack,
    isAffirmDisabled,
    isContinueButtonEnabled,
    isKatapultDisabled,
    isKlarnaDisabled,
    isLoading,
    isPaypalLoaderActive,
    isPlaceOrderDisabledDueTo539,
    isSaveBillingSuccess,
    isSynchronyDisabled,
    isSynchronyPayment,
    isUserInfoFormValid,
    paymentError,
    paymentErrorTitle,
    paymentPayload,
    paypalInfoModalOpen,
    selectedPaymentMethod: paymentType,
    setBillingInfo,
    setCartShippingInfo,
    setIsAffirmDisabled,
    setIsContinueButtonEnabled,
    setIsKatapultDisabled,
    setIsKlarnaDisabled,
    setIsLoading,
    setIsPaypalLoaderActive,
    setIsSaveBillingSuccess,
    setIsSynchronyDisabled,
    setIsSynchronyPayment,
    setIsUserInfoFormValid,
    setPaymentError,
    setPaymentPayload,
    setPaypalInfoModalOpen,
    setSubmitTracker,
    setUserInfoFormValues,
    submitTracker,
    updatePaymentError,
    updatePaymentErrorTitle,
    userInfoFormValues,
  };
}

export function PaymentContextProvider({
  children,
  paymentType,
  initialBillingInfo,
  siteShipping,
  userIp,
}: PaymentProviderProps) {
  const value = useContextSetup({
    initialBillingInfo,
    paymentType,
    siteShipping,
  });

  return (
    <PaymentContext.Provider value={value}>
      <PaymentEffectForMainSite
        initialBillingInfo={initialBillingInfo}
        paymentType={paymentType}
        siteShipping={siteShipping}
        userIp={userIp}
      />
      {children}
    </PaymentContext.Provider>
  );
}

export const usePaymentContextSelector = <SelectedValue,>(
  selector: Selector<PaymentContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<PaymentContextProps, SelectedValue>(
    PaymentContext,
    selector,
    equalCompareFn,
  );
