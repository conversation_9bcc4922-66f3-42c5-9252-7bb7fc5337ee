/* eslint-disable sort-keys */
import { SiteCartBillingAddressType } from '~/data/models/SiteCartBilling';
import { SiteCartBillingRequest } from '~/data/models/SiteCartBillingRequest';
import { SiteCartCouponItem } from '~/data/models/SiteCartCouponItem';
import { SiteCartShipping } from '~/data/models/SiteCartShipping';
import {
  SHIPPINGSERIVCES,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import logger from '~/lib/helpers/logger';
import { isBrowser } from '~/lib/utils/browser';

import { KATAPULT_FORM_FIELDS } from '../../checkout.constants';
import { mapFullNameStateToAbbr } from '../../checkout.util';
import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import {
  KatapulCheckout,
  KatapultCheckoutObject,
  KatapultDiscount,
  KatapultItem,
  KatapultShipping,
} from './KatapultPayment.types';
import { KatapultFormValues } from './KatapultPaymentForm/KatapultPaymentForm';

export const KATAPULT_BUTTON_ID = 'katapult_button_id';
export const KATAPULT_ASSIST_BUTTON_ID = 'katapult_assist_button_id';

export const mapBillingFormToKatapultShipping = (
  obj: KatapultFormValues,
  shippingInfo: SiteCartShippingResponse,
  userInfoFormValues: UserInfoFormValues,
): KatapultShipping => {
  return {
    address: obj.addressLine1,
    address2: obj.addressLine2,
    city: obj.city,
    country: 'United States',
    email: shippingInfo.cartShipping.email || userInfoFormValues.email,
    phone: userInfoFormValues.phone,
    first_name: obj.first_name,
    last_name: obj.last_name,
    middle_name: obj.last_name,
    state: obj.state,
    zip: obj.zip,
  };
};

export const mapCartShippingToKatapultShipping = ({
  shippingInfo,
  userInfoFormValues,
}: {
  shippingInfo: SiteCartShippingResponse;
  userInfoFormValues: UserInfoFormValues;
}): KatapultShipping => {
  const { cartShipping } = shippingInfo;
  return {
    address: cartShipping.addressLine1,
    address2: cartShipping.addressLine2 ?? '',
    city: cartShipping.city,
    country: 'United States',
    email: cartShipping.email || userInfoFormValues?.email,
    phone: cartShipping.phone || userInfoFormValues?.phone,
    first_name: cartShipping.firstName,
    last_name: cartShipping.lastName,
    middle_name: '',
    state: mapFullNameStateToAbbr(cartShipping.state),
    zip: cartShipping.zip,
  };
};

export const mapCouponToKatapult = (
  coupons: SiteCartCouponItem[],
): KatapultDiscount[] => {
  const validCoupons = coupons.filter((coupon) => !coupon.errorDescription);
  return validCoupons.reduce((acc, current) => {
    const katapultDiscount: KatapultDiscount = {
      discount_amount: current.discountInCents
        ? current.discountInCents / 100
        : 0,
      discount_name: current.promoCode,
    } as KatapultDiscount;

    acc.push(katapultDiscount);
    return acc;
  }, [] as KatapultDiscount[]);
};

export const mapCheckoutToKatapultItem = (
  cartSummary: SiteCartSummary,
): KatapulCheckout => {
  return {
    customer_id: cartSummary.id.toString() ?? '',
    discounts: mapCouponToKatapult(cartSummary.siteCartCoupons),
    shipping_amount: cartSummary.shippingCostInCents / 100,
  };
};

export const mapKatapultDeliveryType = (
  siteCartShippingResponse: SiteCartShippingResponse,
): SHIPPINGSERIVCES => {
  if (siteCartShippingResponse.shippingOption == 'fedex') {
    return SHIPPINGSERIVCES.FEDEX;
  }
  if (siteCartShippingResponse.shippingOption == 'home') {
    return SHIPPINGSERIVCES.SHIPTOME;
  }
  if (siteCartShippingResponse.shippingOption == 'installer') {
    if (siteCartShippingResponse?.cartShipping?.installer?.isMobileInstall) {
      return SHIPPINGSERIVCES.MOBILEINSTALL;
    }
    return SHIPPINGSERIVCES.SHIPTOSHOP;
  }
  return SHIPPINGSERIVCES.SHIPTOME;
};

export const mapProductsToKatapultItem = (
  cartSummary: SiteCartSummary,
): KatapultItem[] => {
  const katapultItem: KatapultItem[] = cartSummary.siteProducts.map(
    (product) =>
      ({
        display_name: `${product.name}  ${product.size}`,
        leasable: true,
        quantity: product.quantity,
        sku: `${product.productId}`,
        unit_price: Number(product.price?.salePriceInCents) / 100 || 0,
      }) as KatapultItem,
  );
  if (cartSummary.installationCostInCents > 0) {
    katapultItem.push({
      display_name: 'Tire installation',
      leasable: false,
      sku: 'tire installation',
      quantity: 1,
      unit_price: Number(cartSummary.installationCostInCents) / 100 || 0,
    } as KatapultItem);
  }
  if (cartSummary.estimatedTaxInCents > 0) {
    katapultItem.push({
      display_name: 'Tax',
      leasable: false,
      sku: 'tax',
      quantity: 1,
      unit_price: Number(cartSummary.estimatedTaxInCents) / 100 || 0,
    } as KatapultItem);
  }
  if (cartSummary.estimatedFetTaxInCents > 0) {
    katapultItem.push({
      display_name: 'Fet tax',
      leasable: false,
      sku: 'fet tax',
      quantity: 1,
      unit_price: Number(cartSummary.estimatedFetTaxInCents) / 100 || 0,
    } as KatapultItem);
  }
  if (cartSummary.roadHazardCostInCents > 0) {
    katapultItem.push({
      display_name: 'Road hazard',
      leasable: false,
      sku: 'road hazard',
      quantity: 1,
      unit_price: Number(cartSummary.roadHazardCostInCents) / 100 || 0,
    } as KatapultItem);
  }

  if (cartSummary.estimatedStateFeesInCents > 0) {
    katapultItem.push({
      display_name: 'State fees',
      leasable: false,
      sku: 'state fees',
      quantity: 1,
      unit_price: Number(cartSummary.estimatedStateFeesInCents) / 100 || 0,
    } as KatapultItem);
  }

  return katapultItem;
};

const port = isBrowser()
  ? window.location.port
    ? ':' + window.location.port
    : ''
  : '';
const hostUrl = isBrowser()
  ? `${window.location.protocol}//${window.location.hostname}${port}`
  : '';

export const mapToKatapultCheckoutObject = (
  formValues: KatapultFormValues,
  cartSummary: SiteCartSummary,
  shippingInfo: SiteCartShippingResponse,
  userInfoFormValues: UserInfoFormValues,
): KatapultCheckoutObject => {
  const KatapultCheckoutObject: KatapultCheckoutObject = {
    customer: {
      billing: mapBillingFormToKatapultShipping(
        formValues,
        shippingInfo,
        userInfoFormValues,
      ),
      shipping: mapCartShippingToKatapultShipping({
        shippingInfo,
        userInfoFormValues,
      }),
    },
    items: mapProductsToKatapultItem(cartSummary),
    checkout: mapCheckoutToKatapultItem(cartSummary),
    delivery_type: mapKatapultDeliveryType(shippingInfo),
    urls: {
      return: `${hostUrl}/api/checkout/katapult-confirmation`,
      cancel: `${hostUrl}/checkout/payments#error`,
    },
  };
  logger.info('KatapultCheckoutObject', KatapultCheckoutObject);
  return KatapultCheckoutObject;
};

export const mapShippingAddressToKatapultForm = (
  cartShipping: SiteCartShipping,
): KatapultFormValues => {
  return {
    [KATAPULT_FORM_FIELDS.FIRST_NAME]: cartShipping.firstName,
    [KATAPULT_FORM_FIELDS.LAST_NAME]: cartShipping.lastName,
    [KATAPULT_FORM_FIELDS.ADDRESS]: cartShipping.addressLine1,
    [KATAPULT_FORM_FIELDS.ADDRESS2]: cartShipping.addressLine2 || '',
    [KATAPULT_FORM_FIELDS.STATE]: cartShipping.state,
    [KATAPULT_FORM_FIELDS.ZIP]: cartShipping.zip,
    [KATAPULT_FORM_FIELDS.PO_NUMBER]: '',
    [KATAPULT_FORM_FIELDS.CITY]: cartShipping.city || '',
    [KATAPULT_FORM_FIELDS.EMAIL]: cartShipping.email || '',
  };
};

export const mapKatapultFormValueToCartBillingRequest = (
  formValues: KatapultFormValues,
  userInfoFormValues: UserInfoFormValues,
): SiteCartBillingRequest => {
  return {
    addressLine1: formValues.addressLine1,
    addressLine2: formValues.addressLine2,
    addressType: SiteCartBillingAddressType.BILLING,
    city: formValues.city,
    firstName: formValues.first_name,
    lastName: formValues.last_name,
    phone: userInfoFormValues.phone,
    state: formValues.state,
    zip: formValues.zip,
  };
};
