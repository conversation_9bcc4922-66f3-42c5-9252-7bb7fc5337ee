import { parseCookies } from 'nookies';
import { ReactNode, useCallback, useEffect, useState } from 'react';

import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import {
  apiCreateSiteCartBilling,
  apiGetSiteCartBilling,
  apiUpdateSiteCartBilling,
} from '~/lib/api/checkout/cart-billing';
import { COOKIES } from '~/lib/constants/cookies';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { mapKatapultFormValueToCartBillingRequest } from './KatapultPayment.utils';
import { KatapultFormValues } from './KatapultPaymentForm/KatapultPaymentForm';

export interface KatapultContextProps {
  billingInfo?: SiteCartBillingResponse;
  createBillingInfo: (
    value: KatapultFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => void;
  getBillingInfo: () => Promise<void>;
  updateBillingInfo: (
    value: KatapultFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => Promise<void>;
}

const KatapultContext = createContext<KatapultContextProps>();

function useContextSetup(): KatapultContextProps {
  const [billingInfo, setBillingInfo] = useState<SiteCartBillingResponse>();
  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;

  const getBillingInfo = useCallback(async (): Promise<void> => {
    if (!storedCartId) {
      return;
    }

    const cartBillingResponse = await apiGetSiteCartBilling({
      query: { cartId: storedCartId },
    });

    if (cartBillingResponse.isSuccess) {
      setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
    } else {
      throw new Error(cartBillingResponse.error.message);
    }
  }, [storedCartId]);

  const createBillingInfo = useCallback(
    async (
      formValues: KatapultFormValues,
      userInfoFormValues: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapKatapultFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiCreateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  const updateBillingInfo = useCallback(
    async (
      formValues: KatapultFormValues,
      userInfoFormValues: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapKatapultFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiUpdateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  useEffect(() => {
    (async () => {
      await getBillingInfo();
    })();
  }, [getBillingInfo]);

  return {
    billingInfo,
    getBillingInfo,
    createBillingInfo,
    updateBillingInfo,
  };
}

interface KatapultContextProviderProps {
  children: ReactNode;
}

export function KatapultContextProvider({
  children,
}: KatapultContextProviderProps) {
  const value = useContextSetup();

  return (
    <KatapultContext.Provider value={value}>
      {children}
    </KatapultContext.Provider>
  );
}

export const useKatapultContextSelector = <SelectedValue,>(
  selector: Selector<KatapultContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<KatapultContextProps, SelectedValue>(
    KatapultContext,
    selector,
    equalCompareFn,
  );
