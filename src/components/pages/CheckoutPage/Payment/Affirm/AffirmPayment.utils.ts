import { SiteCartBillingAddressType } from '~/data/models/SiteCartBilling';
import { SiteCartBillingRequest } from '~/data/models/SiteCartBillingRequest';
import { SiteCartCouponItem } from '~/data/models/SiteCartCouponItem';
import { SiteCartProductItem } from '~/data/models/SiteCartProductItem';
import { SiteCartShipping } from '~/data/models/SiteCartShipping';
import { SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';

import { AFFIRM_FORM_FIELDS } from '../../checkout.constants';
import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import {
  AffirmCheckoutObject,
  AffirmDiscount,
  AffirmItem,
  AffirmShipping,
} from './AffirmPayment.types';
import { devAffirmMerchant } from './AffirmPaymentContainer.data';
import { AffirmFormValues } from './AffirmPaymentForm/AffirmPaymentForm';

export const AFFIRM_BUTTON_ID = 'affirm_button_id';
export const AFFIRM_ASSIST_BUTTON_ID = 'affirm_assist_button_id';

export const mapBillingFormToAffirmShipping = (
  obj: AffirmFormValues,
  shippingInfo: SiteCartShippingResponse,
  cartSummary: SiteCartSummary,
): AffirmShipping => {
  return {
    address: {
      city: obj.city,
      country: 'US',
      line1: obj.addressLine1,
      line2: obj.addressLine2 ?? '',
      state: obj.state,
      zipcode: obj.zip,
    },
    email: cartSummary.email || shippingInfo.cartShipping.email || obj.email,
    name: {
      first: obj.firstName,
      last: obj.lastName,
    },
  };
};

export const mapProductsToAffirmItem = (
  products: SiteCartProductItem[],
): AffirmItem[] => {
  return products.map(
    (product) =>
      ({
        categories: [['Tire']],
        display_name: product.name,
        item_image_url: `/${product.image?.src}`,
        item_url: `/${product.link?.href}`,
        qty: product.quantity,
        sku: `${product.productId}`,
        unit_price: Number(product.price?.salePriceInCents) || 0,
      }) as AffirmItem,
  );
};

export const mapCouponToAffirm = (
  coupons: SiteCartCouponItem[],
): Record<string, AffirmDiscount> => {
  const validCoupons = coupons.filter((coupon) => coupon.errorDescription);

  return validCoupons.reduce(
    (acc, current) => {
      const affirmDiscount: AffirmDiscount = {
        discount_amount: current.discountInCents,
        discount_display_name: current.promoCode,
      } as AffirmDiscount;

      acc[current.promoCode] = affirmDiscount;
      return acc;
    },
    {} as Record<string, AffirmDiscount>,
  );
};

export const mapToAffirmCheckoutObject = (
  formValues: AffirmFormValues,
  cartSummary: SiteCartSummary,
  shippingInfo: SiteCartShippingResponse,
): AffirmCheckoutObject => {
  const affirmCheckoutObject: AffirmCheckoutObject = {
    billing: mapBillingFormToAffirmShipping(
      formValues,
      shippingInfo,
      cartSummary,
    ),
    currency: 'USD',
    discounts: mapCouponToAffirm(cartSummary.siteCartCoupons),
    financing_program: 'flyus_3z6r12r',
    items: mapProductsToAffirmItem(cartSummary.siteProducts),
    merchant: devAffirmMerchant,
    order_id: cartSummary.cartUuid,
    shipping: mapBillingFormToAffirmShipping(
      formValues,
      shippingInfo,
      cartSummary,
    ),
    shipping_amount: cartSummary.shippingCostInCents,
    tax_amount: cartSummary.estimatedTaxInCents,
    total: cartSummary.totalInCents,
  };
  return affirmCheckoutObject;
};

export const mapShippingAddressToAffirmForm = (
  cartShipping: SiteCartShipping,
): AffirmFormValues => {
  return {
    [AFFIRM_FORM_FIELDS.FIRST_NAME]: cartShipping.firstName,
    [AFFIRM_FORM_FIELDS.LAST_NAME]: cartShipping.lastName,
    [AFFIRM_FORM_FIELDS.ADDRESS1]: cartShipping.addressLine1,
    [AFFIRM_FORM_FIELDS.ADDRESS2]: cartShipping.addressLine2 || '',
    [AFFIRM_FORM_FIELDS.STATE]: cartShipping.state,
    [AFFIRM_FORM_FIELDS.ZIP]: cartShipping.zip,
    [AFFIRM_FORM_FIELDS.EMAIL]: cartShipping.email,
    [AFFIRM_FORM_FIELDS.PO_NUMBER]: '',
    [AFFIRM_FORM_FIELDS.CITY]: cartShipping.city || '',
  };
};

export const mapAffirmFormValueToCartBillingRequest = (
  formValues: AffirmFormValues,
  userInfoFormValues?: UserInfoFormValues,
): SiteCartBillingRequest => {
  return {
    addressLine1: formValues.addressLine1,
    addressLine2: formValues.addressLine2,
    addressType: SiteCartBillingAddressType.BILLING,
    city: formValues.city,
    firstName: formValues.firstName,
    lastName: formValues.lastName,
    phone: userInfoFormValues?.phone,
    state: formValues.state,
    zip: formValues.zip,
  };
};
