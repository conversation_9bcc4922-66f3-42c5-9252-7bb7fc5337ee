import { parseCookies } from 'nookies';
import { ReactNode, useCallback, useEffect, useState } from 'react';

import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import {
  apiCreateSiteCartBilling,
  apiGetSiteCartBilling,
  apiUpdateSiteCartBilling,
} from '~/lib/api/checkout/cart-billing';
import { COOKIES } from '~/lib/constants/cookies';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { mapAffirmFormValueToCartBillingRequest } from './AffirmPayment.utils';
import { AffirmFormValues } from './AffirmPaymentForm/AffirmPaymentForm';

export interface AffirmContextProps {
  billingInfo?: SiteCartBillingResponse;
  createBillingInfo: (
    value: AffirmFormValues,
    userInfoFormValues?: UserInfoFormValues,
  ) => void;
  getBillingInfo: () => Promise<void>;
  updateBillingInfo: (
    value: AffirmFormValues,
    userInfoFormValues?: UserInfoFormValues,
  ) => Promise<void>;
}

const AffirmContext = createContext<AffirmContextProps>();

function useContextSetup(): AffirmContextProps {
  const [billingInfo, setBillingInfo] = useState<SiteCartBillingResponse>();
  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;

  const getBillingInfo = useCallback(async (): Promise<void> => {
    if (!storedCartId) {
      return;
    }

    const cartBillingResponse = await apiGetSiteCartBilling({
      query: { cartId: storedCartId },
    });

    if (cartBillingResponse.isSuccess) {
      setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
    } else {
      throw new Error(cartBillingResponse.error.message);
    }
  }, [storedCartId]);

  const createBillingInfo = useCallback(
    async (
      formValues: AffirmFormValues,
      userInfoFormValues?: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapAffirmFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiCreateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  const updateBillingInfo = useCallback(
    async (
      formValues: AffirmFormValues,
      userInfoFormValues?: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapAffirmFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiUpdateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  useEffect(() => {
    (async () => {
      await getBillingInfo();
    })();
  }, [getBillingInfo]);

  return {
    billingInfo,
    getBillingInfo,
    createBillingInfo,
    updateBillingInfo,
  };
}

interface AffirmContextProviderProps {
  children: ReactNode;
}

export function AffirmContextProvider({
  children,
}: AffirmContextProviderProps) {
  const value = useContextSetup();

  return (
    <AffirmContext.Provider value={value}>{children}</AffirmContext.Provider>
  );
}

export const useAffirmContextSelector = <SelectedValue,>(
  selector: Selector<AffirmContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<AffirmContextProps, SelectedValue>(
    AffirmContext,
    selector,
    equalCompareFn,
  );
