import { SiteCartBillingAddressType } from '~/data/models/SiteCartBilling';
import { SiteCartBillingRequest } from '~/data/models/SiteCartBillingRequest';

import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { BillingInfoValues } from '../BillingInfoForm/BillingInfo.types';

export const SIMPLE_PAY_ASSIST_BUTTON_ID = 'SIMPLE_PAY_ASSIST_BUTTON_ID';
export const SIMPLE_PAY_BUTTON_ID = 'SIMPLE_PAY_BUTTON_ID';

export function mapBillingInfoToBillingRequest(
  billingInfo?: Partial<BillingInfoValues>,
  userInfoFormValues?: UserInfoFormValues,
): SiteCartBillingRequest {
  return {
    addressLine1: billingInfo?.addressLine1 ?? '',
    addressLine2: billingInfo?.addressLine2 ?? '',
    addressType: SiteCartBillingAddressType.BILLING,
    city: billingInfo?.city ?? '',
    firstName: billingInfo?.firstName ?? '',
    lastName: billingInfo?.lastName ?? '',
    phone: userInfoFormValues?.phone,
    phoneType: billingInfo?.phoneType ?? undefined,
    poNumber: billingInfo?.poNumber ?? '',
    state: billingInfo?.state ?? '',
    zip: billingInfo?.zip ?? '',
  };
}
