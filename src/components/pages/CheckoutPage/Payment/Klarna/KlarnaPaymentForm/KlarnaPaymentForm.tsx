import { createRef, RefObject, useCallback, useEffect, useState } from 'react';

import Button from '~/components/global/Button/Button';
import Checkbox from '~/components/global/Checkbox/Checkbox';
import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Input from '~/components/global/Input/Input';
import BaseLink from '~/components/global/Link/BaseLink';
import Loading from '~/components/global/Loading/Loading';
import SmartyStreetAddressForm from '~/components/global/SmartyStreetAddressForm/SmartyStreetAddressForm';
import { SmartyFormValues } from '~/components/global/SmartyStreetAddressForm/SmartyStreetAddressForm.types';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { SiteCartShipping } from '~/data/models/SiteCartShipping';
import { ShippingType } from '~/data/models/SiteCartShippingResponse';
import { useHasChanged } from '~/hooks/useHasChanged';
import useWidgetSource from '~/hooks/useWigetSource';
import { PAYMENT_OPTIONS } from '~/lib/constants/payment-options';
import { THEME } from '~/lib/constants/theme';
import { TIME } from '~/lib/constants/time';
import { scrollToRef } from '~/lib/helpers/scroll';
import {
  address,
  city,
  email,
  firstName,
  lastName,
  zipCode,
} from '~/lib/utils/regex';
import { ui } from '~/lib/utils/ui-dictionary';

import { KLARNA_FORM_FIELDS } from '../../../checkout.constants';
import { isValidState } from '../../../checkout.util';
import { UserInfoFormValues } from '../../../Payments/UserInfoForm/UserInfoForm.types';
import formStyles from '../../../Shipping/ShippingForNonInstallableTires/ShipToMe/ShipToMe.styles';
import { BillingInfoValues } from '../../BillingInfoForm/BillingInfo.types';
import btnStyles from '../../PaymentActionBar/PaymentActionBar.styles';
import { usePaymentContextSelector } from '../../PaymentContext/Payment.context';
import {
  KLARNA_ASSIST_BUTTON_ID,
  KLARNA_BUTTON_ID,
  mapShippingAddressToKlarnaForm,
} from '../KlarnaPayment.utils';
import styles from './KlarnaPaymentForm.styles';

export interface KlarnaFormValues {
  [KLARNA_FORM_FIELDS.FIRST_NAME]: string;
  [KLARNA_FORM_FIELDS.LAST_NAME]: string;
  [KLARNA_FORM_FIELDS.ADDRESS1]: string;
  [KLARNA_FORM_FIELDS.ADDRESS2]: string;
  [KLARNA_FORM_FIELDS.STATE]: string;
  [KLARNA_FORM_FIELDS.ZIP]: string;
  [KLARNA_FORM_FIELDS.CITY]: string;
  [KLARNA_FORM_FIELDS.EMAIL]: string;
}

const ERRORS = {
  [KLARNA_FORM_FIELDS.FIRST_NAME]: ui('checkout.paymentType.error.firstName'),
  [KLARNA_FORM_FIELDS.LAST_NAME]: ui('checkout.paymentType.error.lastName'),
  [KLARNA_FORM_FIELDS.ADDRESS1]: ui('checkout.paymentType.error.Address1'),
  [KLARNA_FORM_FIELDS.STATE]: ui('checkout.paymentType.error.state'),
  [KLARNA_FORM_FIELDS.ZIP]: ui('checkout.paymentType.error.zip'),
  [KLARNA_FORM_FIELDS.CITY]: ui('checkout.paymentType.error.city'),
  [KLARNA_FORM_FIELDS.EMAIL]: ui('checkout.paymentType.error.email'),
};

const initialErrorState = {
  [KLARNA_FORM_FIELDS.FIRST_NAME]: '',
  [KLARNA_FORM_FIELDS.LAST_NAME]: '',
  [KLARNA_FORM_FIELDS.ADDRESS1]: '',
  [KLARNA_FORM_FIELDS.ADDRESS2]: '',
  [KLARNA_FORM_FIELDS.STATE]: '',
  [KLARNA_FORM_FIELDS.ZIP]: '',
  [KLARNA_FORM_FIELDS.CITY]: '',
  [KLARNA_FORM_FIELDS.EMAIL]: '',
};

interface Props {
  cartShipping?: SiteCartShipping;
  hasNoAppointment: boolean | undefined;
  hasValidShippingPlace: boolean;
  initialBillingInfo: BillingInfoValues;
  isSubmitting: boolean;
  onContinue: (
    formValue: KlarnaFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => void;
  shippingOption?: ShippingType;
}

function PaymentsTypeForm({
  initialBillingInfo,
  isSubmitting,
  onContinue,
  cartShipping,
  shippingOption,
  hasValidShippingPlace,
  hasNoAppointment,
}: Props) {
  const { isComingFromWidget } = useWidgetSource();

  const { setIsKlarnaDisabled, userInfoFormValues, isUserInfoFormValid } =
    usePaymentContextSelector((v) => ({
      setIsKlarnaDisabled: v.setIsKlarnaDisabled,
      userInfoFormValues: v.userInfoFormValues,
      isUserInfoFormValid: v.isUserInfoFormValid,
    }));

  const { currentTab, isShipToMeTabSelected, setIsShippingFormOpen } =
    useCartUserActionContextSelector((v) => ({
      currentTab: v.currentTab,
      isShipToMeTabSelected: v.isShipToMeTabSelected,
      setIsShippingFormOpen: v.setIsShippingFormOpen,
    }));

  const currentTabChanged = useHasChanged(currentTab);

  const initialState = {
    [KLARNA_FORM_FIELDS.FIRST_NAME]: initialBillingInfo.firstName,
    [KLARNA_FORM_FIELDS.LAST_NAME]: initialBillingInfo.lastName,
    [KLARNA_FORM_FIELDS.ADDRESS1]: initialBillingInfo.addressLine1,
    [KLARNA_FORM_FIELDS.ADDRESS2]: initialBillingInfo.addressLine2,
    [KLARNA_FORM_FIELDS.EMAIL]: '',
    [KLARNA_FORM_FIELDS.STATE]: initialBillingInfo.state,
    [KLARNA_FORM_FIELDS.ZIP]: initialBillingInfo.zip,
    [KLARNA_FORM_FIELDS.CITY]: initialBillingInfo.city,
  };
  const shippingAndBillingInfoIsSame =
    initialBillingInfo.firstName === cartShipping?.firstName &&
    initialBillingInfo.lastName === cartShipping?.lastName &&
    initialBillingInfo.addressLine1 === cartShipping?.addressLine1 &&
    initialBillingInfo.addressLine2 === cartShipping?.addressLine2 &&
    initialBillingInfo.state === cartShipping?.state &&
    initialBillingInfo.zip === cartShipping?.zip &&
    initialBillingInfo.city === cartShipping?.city;
  const [formValues, setFormValues] = useState<KlarnaFormValues>(initialState);
  const [errors, setErrors] = useState(initialErrorState);
  const [isSameAddress, setIsSameAddress] = useState(
    shippingAndBillingInfoIsSame,
  );
  const [inputElementRefs, setInputElementRefs] = useState<
    Record<string, RefObject<HTMLDivElement | null>>
  >({});
  const [isSubmitted, setIsSubmitted] = useState<number>(0);
  const [isFormValid, setIsFormValid] = useState<boolean>(false);

  const hasValidFirstName = firstName.test(
    formValues[KLARNA_FORM_FIELDS.FIRST_NAME] || '',
  );
  const hasValidLastName = lastName.test(
    formValues[KLARNA_FORM_FIELDS.LAST_NAME] || '',
  );
  const hasValidAddress1 = address.test(
    formValues[KLARNA_FORM_FIELDS.ADDRESS1] || '',
  );
  const hasValidCity = city.test(formValues[KLARNA_FORM_FIELDS.CITY] || '');
  const hasValidEmail = email.test(formValues[KLARNA_FORM_FIELDS.EMAIL] || '');
  const hasValidZip = zipCode.test(formValues[KLARNA_FORM_FIELDS.ZIP] || '');
  const hasValidState = isValidState(formValues[KLARNA_FORM_FIELDS.STATE]);

  useEffect(() => {
    setIsFormValid(
      hasValidFirstName &&
        hasValidLastName &&
        hasValidAddress1 &&
        hasValidCity &&
        hasValidState &&
        hasValidZip &&
        !hasNoAppointment &&
        hasValidShippingPlace &&
        hasValidEmail,
    );
  }, [
    formValues,
    hasNoAppointment,
    hasValidAddress1,
    hasValidCity,
    hasValidEmail,
    hasValidFirstName,
    hasValidLastName,
    hasValidShippingPlace,
    hasValidState,
    hasValidZip,
  ]);

  const handleAddressFormChange = (values: SmartyFormValues) => {
    setErrors((prev) => ({ ...prev, addressLine1: '' }));
    setFormValues({ ...formValues, ...values });
  };

  const handleCheckboxChange = (value?: boolean) => {
    setIsSameAddress(value || false);
    if (value && cartShipping) {
      setFormValues(mapShippingAddressToKlarnaForm(cartShipping));
    } else {
      setFormValues(initialState);
    }
  };

  const handleSetFormFieldValue =
    (fieldName: string) => (value: string | null) => {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: '',
      }));

      setFormValues({
        ...formValues,
        [fieldName]: value,
      });
    };

  const handleClickAssistButton = useCallback(() => {
    setIsSubmitted((prev) => (prev += 1));
    if (!cartShipping && isShipToMeTabSelected && isComingFromWidget) {
      setIsShippingFormOpen(true);
    }
    const newErrorState = {
      [KLARNA_FORM_FIELDS.FIRST_NAME]: hasValidFirstName
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.FIRST_NAME],
      [KLARNA_FORM_FIELDS.LAST_NAME]: hasValidLastName
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.LAST_NAME],
      [KLARNA_FORM_FIELDS.ADDRESS1]: hasValidAddress1
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.ADDRESS1],
      [KLARNA_FORM_FIELDS.ADDRESS2]: '',
      [KLARNA_FORM_FIELDS.CITY]: hasValidCity
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.CITY],
      [KLARNA_FORM_FIELDS.STATE]: hasValidState
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.STATE],
      [KLARNA_FORM_FIELDS.EMAIL]: hasValidEmail
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.EMAIL],
      [KLARNA_FORM_FIELDS.ZIP]: hasValidZip
        ? ''
        : ERRORS[KLARNA_FORM_FIELDS.ZIP],
    };

    for (const key in newErrorState) {
      if (inputElementRefs[key] && inputElementRefs[key].current) {
        scrollToRef(
          inputElementRefs[key] as RefObject<HTMLDivElement | null>,
          TIME.MS400,
        );
        break;
      }
    }

    setErrors((prev) => ({
      ...prev,
      ...newErrorState,
    }));
  }, [
    cartShipping,
    hasValidAddress1,
    hasValidCity,
    hasValidEmail,
    hasValidFirstName,
    hasValidLastName,
    hasValidState,
    hasValidZip,
    inputElementRefs,
    isShipToMeTabSelected,
    isComingFromWidget,
    setIsShippingFormOpen,
  ]);

  useEffect(() => {
    Object.keys(initialErrorState).forEach((key) => {
      setInputElementRefs((prev) => ({ ...prev, [key]: createRef() }));
    });
  }, []);

  useEffect(() => {
    setIsKlarnaDisabled(!isFormValid);
  }, [isFormValid, setIsKlarnaDisabled]);

  useEffect(() => {
    if (!currentTabChanged) {
      return;
    }

    setErrors(initialErrorState);
  }, [currentTabChanged]);

  useEffect(() => {
    setFormValues((prev) => ({
      ...prev,
      [KLARNA_FORM_FIELDS.EMAIL]: userInfoFormValues.email,
    }));
  }, [userInfoFormValues.email]);

  const handleContinue = useCallback(() => {
    setIsSubmitted((prev) => (prev += 1));
    if (!isFormValid) {
      handleClickAssistButton();
      return;
    }
    onContinue(formValues, userInfoFormValues);
  }, [
    isFormValid,
    onContinue,
    formValues,
    userInfoFormValues,
    handleClickAssistButton,
  ]);

  useEffect(() => {
    const assistButton = document.querySelector(
      `[id=${KLARNA_ASSIST_BUTTON_ID}]`,
    );
    const submitButton = document.querySelector(`[id=${KLARNA_BUTTON_ID}]`);

    assistButton?.addEventListener('click', handleClickAssistButton);

    submitButton?.addEventListener('click', handleContinue);

    return () => {
      assistButton?.removeEventListener('click', handleClickAssistButton);
      submitButton?.removeEventListener('click', handleContinue);
    };
  }, [handleClickAssistButton, handleContinue]);

  return (
    <Grid css={formStyles.root}>
      <GridItem fullbleed>
        <form>
          {shippingOption === ShippingType.HOME && (
            <label css={styles.checkLabel}>
              <Checkbox
                checked={isSameAddress}
                onChange={handleCheckboxChange}
              />
              <span css={[styles.checkTitle, styles.ml10]}>
                {ui('checkout.payments.confirmAddress')}
              </span>
            </label>
          )}
          <p css={styles.formTitle}>{ui('checkout.payments.billingInfo')}</p>
          <fieldset css={formStyles.group}>
            <div
              css={[
                formStyles.input,
                !!errors[KLARNA_FORM_FIELDS.FIRST_NAME] &&
                  formStyles.inputHasError,
              ]}
              ref={inputElementRefs[KLARNA_FORM_FIELDS.FIRST_NAME]}
            >
              <Input
                id={PAYMENT_OPTIONS.KLARNA + KLARNA_FORM_FIELDS.FIRST_NAME}
                value={formValues[KLARNA_FORM_FIELDS.FIRST_NAME]}
                onChange={handleSetFormFieldValue(
                  KLARNA_FORM_FIELDS.FIRST_NAME,
                )}
                label={ui('checkout.shipping.shipToMeForm.firstName')}
                error={{
                  hasError: !hasValidFirstName,
                  errorMessage: ui('checkout.paymentType.error.firstName'),
                }}
                required
                hasError={!!errors[KLARNA_FORM_FIELDS.FIRST_NAME]}
                customErrorStyle={formStyles.customError}
              />
            </div>
          </fieldset>
          <fieldset css={formStyles.group}>
            <div
              css={[
                formStyles.input,
                !!errors[KLARNA_FORM_FIELDS.LAST_NAME] &&
                  formStyles.inputHasError,
              ]}
              ref={inputElementRefs[KLARNA_FORM_FIELDS.LAST_NAME]}
            >
              <Input
                id={PAYMENT_OPTIONS.KLARNA + KLARNA_FORM_FIELDS.LAST_NAME}
                value={formValues[KLARNA_FORM_FIELDS.LAST_NAME]}
                onChange={handleSetFormFieldValue(KLARNA_FORM_FIELDS.LAST_NAME)}
                label={ui('checkout.shipping.shipToMeForm.lastName')}
                error={{
                  hasError: !hasValidLastName,
                  errorMessage: ui('checkout.paymentType.error.lastName'),
                }}
                required
                hasError={!!errors[KLARNA_FORM_FIELDS.LAST_NAME]}
                customErrorStyle={formStyles.customError}
              />
            </div>
          </fieldset>
          <fieldset css={formStyles.group}>
            <div
              css={[
                formStyles.input,
                !!errors[KLARNA_FORM_FIELDS.ADDRESS1] &&
                  formStyles.inputHasError,
              ]}
              ref={inputElementRefs[KLARNA_FORM_FIELDS.ADDRESS1]}
            >
              <SmartyStreetAddressForm
                isSameAddress={!!isSameAddress}
                isSubmitted={isSubmitted}
                onChange={handleAddressFormChange}
                values={{
                  addressLine1: formValues.addressLine1,
                  addressLine2: formValues.addressLine2,
                  city: formValues.city,
                  state: formValues.state,
                  zip: formValues.zip,
                }}
              />
            </div>
          </fieldset>
        </form>

        <span css={styles.termsOfSale}>
          <span>{ui('checkout.termsOfSale1')}</span>
          <BaseLink href={'/sales'} isExternal>
            <span>
              <strong>terms of sale.</strong>
            </span>
          </BaseLink>
          <span>{ui('checkout.termsOfSale2')}</span>
        </span>

        <div css={styles.cta}>
          <div css={btnStyles.btnWrapper}>
            {!isFormValid && (
              <button
                css={btnStyles.assistBtn}
                onClick={handleClickAssistButton}
                aria-hidden
              />
            )}
            <Button
              isDisabled={!isFormValid || isSubmitting || !isUserInfoFormValid}
              onClick={handleContinue}
              css={btnStyles.klarnaBtn}
            >
              {isSubmitting ? (
                <Loading theme={THEME.DARK} />
              ) : (
                <Icon css={btnStyles.logo} name={ICONS.KLARNA} />
              )}
            </Button>
          </div>
        </div>
      </GridItem>
    </Grid>
  );
}

export default PaymentsTypeForm;
