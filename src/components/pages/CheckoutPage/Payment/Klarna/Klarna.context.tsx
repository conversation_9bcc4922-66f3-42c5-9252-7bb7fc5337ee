import { parseCookies } from 'nookies';
import { ReactNode, useCallback, useEffect, useState } from 'react';

import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import {
  apiCreateSiteCartBilling,
  apiGetSiteCartBilling,
  apiUpdateSiteCartBilling,
} from '~/lib/api/checkout/cart-billing';
import { COOKIES } from '~/lib/constants/cookies';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { mapKlarnaFormValueToCartBillingRequest } from './KlarnaPayment.utils';
import { KlarnaFormValues } from './KlarnaPaymentForm/KlarnaPaymentForm';

export interface KlarnaContextProps {
  billingInfo?: SiteCartBillingResponse;
  createBillingInfo: (
    value: KlarnaFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => void;
  getBillingInfo: () => Promise<void>;
  updateBillingInfo: (
    value: KlarnaFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => Promise<void>;
}

const KlarnaContext = createContext<KlarnaContextProps>();

function useContextSetup(): KlarnaContextProps {
  const [billingInfo, setBillingInfo] = useState<SiteCartBillingResponse>();
  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;

  const getBillingInfo = useCallback(async (): Promise<void> => {
    if (!storedCartId) {
      return;
    }

    const cartBillingResponse = await apiGetSiteCartBilling({
      query: { cartId: storedCartId },
    });

    if (cartBillingResponse.isSuccess) {
      setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
    }
  }, [storedCartId]);

  const createBillingInfo = useCallback(
    async (
      formValues: KlarnaFormValues,
      userInfoFormValues: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapKlarnaFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiCreateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  const updateBillingInfo = useCallback(
    async (
      formValues: KlarnaFormValues,
      userInfoFormValues: UserInfoFormValues,
    ): Promise<void> => {
      if (!storedCartId) {
        return;
      }
      const input = mapKlarnaFormValueToCartBillingRequest(
        formValues,
        userInfoFormValues,
      );
      const cartBillingResponse = await apiUpdateSiteCartBilling({
        query: { cartId: storedCartId },
        input,
      });

      if (cartBillingResponse.isSuccess) {
        setBillingInfo(cartBillingResponse.data.siteCartBillingResponse);
      } else {
        throw new Error(cartBillingResponse.error.message);
      }
    },
    [storedCartId],
  );

  useEffect(() => {
    (async () => {
      await getBillingInfo();
    })();
  }, [getBillingInfo]);

  return {
    billingInfo,
    getBillingInfo,
    createBillingInfo,
    updateBillingInfo,
  };
}

interface KlarnaContextProviderProps {
  children: ReactNode;
}

export function KlarnaContextProvider({
  children,
}: KlarnaContextProviderProps) {
  const value = useContextSetup();

  return (
    <KlarnaContext.Provider value={value}>{children}</KlarnaContext.Provider>
  );
}

export const useKlarnaContextSelector = <SelectedValue,>(
  selector: Selector<KlarnaContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<KlarnaContextProps, SelectedValue>(
    KlarnaContext,
    selector,
    equalCompareFn,
  );
