/* eslint-disable sort-keys */
import lscache from 'lscache';
import { parseCookies, setCookie } from 'nookies';
import { useCallback, useEffect, useState } from 'react';

import Button from '~/components/global/Button/Button';
import GridItem from '~/components/global/Grid/GridItem';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Notification from '~/components/global/NotificationBanner/Notification';
import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { usePaymentDataContextSelector } from '~/components/modules/PaymentData/PaymentData.context';
import { ServerData } from '~/components/pages/CheckoutPage/Payment/PaymentContainer.types';
import { OrderPaymentType } from '~/data/models/SiteCartOrderRequest';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import useKlarna from '~/hooks/useKlarna';
import useRouter from '~/hooks/useRouter';
import { apiCreateSiteCartOrder } from '~/lib/api/checkout/cart-order';
import { ICON_IMAGE_TYPE } from '~/lib/backend/icon-image.types';
import { BUTTON_STYLE } from '~/lib/constants/buttons.types';
import { COOKIES } from '~/lib/constants/cookies';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { THEME } from '~/lib/constants/theme';
import GA from '~/lib/helpers/analytics';
import logger from '~/lib/helpers/logger';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { isBrowser } from '~/lib/utils/browser';
import { seStorage } from '~/lib/utils/browser-storage';
import { titleCaseSlug } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';

import TrykatapultModal from '../../ModalWithCta/ModalWithCta';
import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { VehicleInfo } from '../../ShippingConfirmation/ShippingConfirmation.types';
import { KATAPULT_BUTTON_ID } from '../Katapult/KatapultPayment.utils';
import { usePaymentContextSelector } from '../PaymentContext/Payment.context';
import paymentErrorStyles from '../PaymentError/PaymentError.styles';
import {
  KlarnaContextProvider,
  useKlarnaContextSelector,
} from './Klarna.context';
import KlarnaCheckoutModal from './KlarnaCheckoutModal';
import styles from './KlarnaPaymentContainer.styles';
import KlarnaPaymentForm, {
  KlarnaFormValues,
} from './KlarnaPaymentForm/KlarnaPaymentForm';

function KlarnaPayment({
  siteCartSummary,
  siteShipping,
  initialBillingInfo,
  userIp,
}: ServerData) {
  const router = useRouter();
  const cookies = parseCookies();
  const forterToken = cookies[COOKIES.FORTER] || null;
  const localVehicleData: VehicleInfo = lscache.get(
    LOCAL_STORAGE[PROPERTIES.VEHICLE_METADATA],
  );
  const {
    vehicleMake = '',
    vehicleModel = '',
    vehicleTrim = '',
    vehicleYear = '',
  } = localVehicleData ?? {};
  const { cartSummaryFromContext, setPaymentType, updateCartSummary } =
    useCartSummaryContextSelector((v) => ({
      cartSummaryFromContext: v.cartSummary,
      setPaymentType: v.setPaymentType,
      updateCartSummary: v.updateCartSummary,
    }));
  const isShippingDataLoading = useCartShippingContextSelector(
    (v) => v.isLoading,
  );
  const { billingInfo, createBillingInfo, updateBillingInfo } =
    useKlarnaContextSelector((v) => ({
      billingInfo: v.billingInfo,
      createBillingInfo: v.createBillingInfo,
      updateBillingInfo: v.updateBillingInfo,
    }));
  const setCrossPageErrorMessage = usePaymentDataContextSelector(
    (v) => v.setCrossPageErrorMessage,
  );
  const [openModal, setOpenModal] = useState<boolean>(false);
  const {
    getKlarnaClientToken,
    klarnaSessionToken,
    setPaymentData,
    klarnaPayload,
    paymentMethodCategories,
  } = usePaymentDataContextSelector((v) => ({
    getKlarnaClientToken: v.getKlarnaClientToken,
    klarnaSessionToken: v.klarnaSessionToken,
    setPaymentData: v.setPaymentData,
    klarnaPayload: v.klarnaPayload,
    paymentMethodCategories: v.paymentMethodCategories,
  }));
  const { hasValidShippingPlace, hasNoAppointment } =
    useCartUserActionContextSelector((v) => ({
      hasValidShippingPlace: v.hasValidShippingPlace,
      hasNoAppointment: v.hasNoAppointment,
    }));

  const { isLoading, setIsLoading, userInfoFormValues } =
    usePaymentContextSelector((v) => ({
      isLoading: v.isLoading,
      setIsLoading: v.setIsLoading,
      userInfoFormValues: v.userInfoFormValues,
    }));
  const [klarnaModalOpen, setKlarnaModalOpen] = useState(false);
  const [showPaymentError, setShowPaymentError] = useState(false);
  const [isKlarnaInitialized, setIsKlarnaInitialized] = useState(false);
  const [klarnaAuthToken, setKlarnaAuthToken] = useState('');
  const { isReady } = useKlarna();
  const KLARNA_API_SCRIPT =
    process.env.KLARNA_API_SCRIPT_URL ||
    'https://x.klarnacdn.net/kp/lib/v1/api.js';

  useEffect(() => {
    getKlarnaClientToken();
  }, [getKlarnaClientToken]);

  useEffect(() => {
    if (
      isReady &&
      klarnaSessionToken &&
      !isKlarnaInitialized &&
      KLARNA_API_SCRIPT
    ) {
      const script = document.createElement('script');
      script.async = true;
      script.defer = true;
      script.src = KLARNA_API_SCRIPT;
      document.body.appendChild(script);

      window.klarnaAsyncCallback = () => {
        if (window.Klarna) {
          window.Klarna.Payments.init({
            client_token: klarnaSessionToken,
          });
        }
      };
      setIsKlarnaInitialized(true);
    }
  }, [klarnaSessionToken, isKlarnaInitialized, isReady, KLARNA_API_SCRIPT]);

  const cartSummary = cartSummaryFromContext || siteCartSummary.siteCart;

  const handleContinue = async (
    formValues: KlarnaFormValues,
    userInfoFormValues: UserInfoFormValues,
  ) => {
    try {
      if (siteShipping && cartSummary) {
        setIsLoading(true);
        if (billingInfo) {
          await updateBillingInfo(formValues, userInfoFormValues);
        } else {
          await createBillingInfo(formValues, userInfoFormValues);
        }
        if (window.Klarna) {
          setKlarnaModalOpen(true);
          setTimeout(() => {
            window.Klarna.Payments.load(
              {
                container: '#klarna-payments-container',
                payment_method_category: paymentMethodCategories?.length
                  ? paymentMethodCategories[0].identifier
                  : 'pay_over_time',
              },
              {
                ...klarnaPayload,
                billing_address: {
                  given_name:
                    billingInfo?.cartBilling?.firstName ||
                    siteShipping?.cartShipping?.firstName ||
                    formValues.firstName,
                  family_name:
                    billingInfo?.cartBilling?.lastName ||
                    siteShipping?.cartShipping?.lastName ||
                    formValues.lastName,
                  email:
                    cartSummary.email ||
                    siteShipping?.cartShipping?.email ||
                    formValues.email ||
                    '',
                  street_address:
                    billingInfo?.cartBilling?.addressLine1 ||
                    siteShipping?.cartShipping?.addressLine1 ||
                    formValues.addressLine1,
                  street_address2:
                    billingInfo?.cartBilling?.addressLine2 ||
                    siteShipping?.cartShipping?.addressLine2 ||
                    formValues.addressLine2,
                  postal_code:
                    billingInfo?.cartBilling?.zip ||
                    siteShipping?.cartShipping?.zip ||
                    formValues.zip,
                  city:
                    billingInfo?.cartBilling?.city ||
                    siteShipping?.cartShipping?.city ||
                    formValues.city,
                  region:
                    formValues?.state ||
                    billingInfo?.cartBilling?.state ||
                    formValues.state,
                  phone:
                    billingInfo?.cartBilling?.phone ||
                    siteShipping?.cartShipping?.phone,
                  country: 'US',
                },
                shipping_address: {
                  given_name:
                    siteShipping?.cartShipping?.firstName ||
                    billingInfo?.cartBilling?.firstName ||
                    formValues.firstName ||
                    '',
                  family_name:
                    siteShipping?.cartShipping?.lastName ||
                    billingInfo?.cartBilling?.lastName ||
                    formValues.lastName ||
                    '',
                  email:
                    cartSummary.email ||
                    siteShipping?.cartShipping?.email ||
                    formValues.email ||
                    '',
                  street_address:
                    siteShipping?.cartShipping?.addressLine1 ||
                    formValues.addressLine1 ||
                    '',
                  street_address2:
                    siteShipping?.cartShipping?.addressLine2 ||
                    formValues.addressLine2 ||
                    '',
                  postal_code:
                    siteShipping?.cartShipping?.zip || formValues.zip || '',
                  city:
                    siteShipping?.cartShipping?.city || formValues.city || '',
                  region:
                    siteShipping?.cartShipping?.state || formValues.state || '',
                  phone:
                    siteShipping?.cartShipping?.phone ||
                    billingInfo?.cartBilling?.phone ||
                    '',
                  country: 'US',
                },
              },
              function (res: { show_form: boolean }) {
                logger.info('klarna load response: ', res);
                if (!res.show_form) {
                  setKlarnaModalOpen(false);
                  setIsLoading(false);
                  setOpenModal(true);
                }
              },
            );
          }, 500);
          if (!cartSummary.email) {
            await updateCartSummary({
              email: userInfoFormValues.email,
              vehicleDescription: userInfoFormValues.vehicle ?? '',
              vehicleMake,
              vehicleModel,
              vehicleTrim,
              vehicleYear,
            });
          }
        } else {
          logger.error('Klarna object not available');
          setOpenModal(true);
          setIsLoading(false);
        }
      }
    } catch (error) {
      logger.error('Klarna error:', error);
      setOpenModal(true);
      setIsLoading(false);
    }
  };

  const handlePlaceOrder = () => {
    if (window.Klarna) {
      logger.info('klarnaPayload: ', klarnaPayload);
      window.Klarna.Payments.authorize(
        {
          payment_method_category: 'pay_over_time',
        },
        {
          ...klarnaPayload,
          billing_address: {
            given_name: billingInfo?.cartBilling?.firstName,
            family_name: billingInfo?.cartBilling?.lastName,
            email:
              cartSummary.email ||
              siteShipping?.cartShipping?.email ||
              userInfoFormValues.email ||
              '',
            street_address: billingInfo?.cartBilling?.addressLine1,
            street_address2: billingInfo?.cartBilling?.addressLine2,
            postal_code: billingInfo?.cartBilling?.zip,
            city: billingInfo?.cartBilling?.city,
            region: billingInfo?.cartBilling?.state,
            phone: billingInfo?.cartBilling?.phone,
            country: 'US',
          },
          shipping_address: {
            given_name:
              siteShipping?.cartShipping?.firstName ||
              billingInfo?.cartBilling?.firstName ||
              '',
            family_name:
              siteShipping?.cartShipping?.lastName ||
              billingInfo?.cartBilling?.lastName ||
              '',
            email:
              cartSummary.email ||
              siteShipping?.cartShipping?.email ||
              userInfoFormValues.email ||
              '',
            street_address: siteShipping?.cartShipping?.addressLine1 || '',
            street_address2: siteShipping?.cartShipping?.addressLine2 || '',
            postal_code: siteShipping?.cartShipping?.zip || '',
            city: siteShipping?.cartShipping?.city || '',
            region: siteShipping?.cartShipping?.state || '',
            phone:
              siteShipping?.cartShipping?.phone ||
              billingInfo?.cartBilling?.phone ||
              '',
            country: 'US',
          },
        },
        function (res: {
          approved: boolean;
          authorization_token: string;
          finalize_required: boolean;
          show_form: boolean;
        }) {
          logger.info('klarna authorize response', res);
          if (res.approved) {
            setKlarnaAuthToken(res.authorization_token);
          } else {
            setOpenModal(true);
          }
          setKlarnaModalOpen(false);
          setIsLoading(false);
        },
      );
    } else {
      setIsLoading(false);
      setOpenModal(true);
    }
  };

  const redirectOderConfirmation = useCallback(async (orderId: number) => {
    await router.replace('/checkout/order-confirmation/' + orderId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (klarnaAuthToken && klarnaPayload) {
      const createCartOrder = async () => {
        const response = await apiCreateSiteCartOrder({
          input: {
            cartId: `${siteCartSummary.siteCart.id}`,
            source:
              seStorage.getItem(SESSION_STORAGE[SESSION_PROPERTIES.SOURCE]) ??
              undefined,
            subSource:
              seStorage.getItem(
                SESSION_STORAGE[SESSION_PROPERTIES.SUB_SOURCE],
              ) ?? undefined,
            widgetSource:
              seStorage.getItem(
                SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
              ) ?? undefined,
            forterToken: (isBrowser() && forterToken) || '',
            klarna: {
              authorizationToken: klarnaAuthToken,
              cartDetails: {
                ...klarnaPayload,
                billing_address: {
                  given_name: billingInfo?.cartBilling?.firstName || '',
                  family_name: billingInfo?.cartBilling?.lastName || '',
                  email:
                    cartSummary.email ||
                    siteShipping?.cartShipping?.email ||
                    userInfoFormValues.email ||
                    '',
                  street_address: billingInfo?.cartBilling?.addressLine1 || '',
                  street_address2: billingInfo?.cartBilling?.addressLine2 || '',
                  postal_code: billingInfo?.cartBilling?.zip || '',
                  city: billingInfo?.cartBilling?.city || '',
                  region: billingInfo?.cartBilling?.state || '',
                  phone: billingInfo?.cartBilling?.phone || '',
                  country: 'US',
                },
                shipping_address: {
                  given_name:
                    siteShipping?.cartShipping?.firstName ||
                    billingInfo?.cartBilling?.firstName ||
                    '',
                  family_name:
                    siteShipping?.cartShipping?.lastName ||
                    billingInfo?.cartBilling?.lastName ||
                    '',
                  email:
                    cartSummary.email ||
                    siteShipping?.cartShipping?.email ||
                    userInfoFormValues.email ||
                    '',
                  street_address:
                    siteShipping?.cartShipping?.addressLine1 || '',
                  street_address2:
                    siteShipping?.cartShipping?.addressLine2 || '',
                  postal_code: siteShipping?.cartShipping?.zip || '',
                  city: siteShipping?.cartShipping?.city || '',
                  region: siteShipping?.cartShipping?.state || '',
                  phone:
                    siteShipping?.cartShipping?.phone ||
                    billingInfo?.cartBilling?.phone ||
                    '',
                  country: 'US',
                },
              },
            },
            paymentType: OrderPaymentType.KLARNA,
            userAgent: (isBrowser() && window.navigator.userAgent) || '',
            userIp,
          },
        });
        setKlarnaAuthToken('');

        if (response.isSuccess) {
          setPaymentData({ paymentType: PAYMENT_OPTIONS.KLARNA });
          GA.addToDataLayer({
            cartSummary,
            customerId: response.data.customerId,
            emailMatchesExistingAccount:
              response.data.emailMatchesExistingAccount,
            event: 'isCheckoutComplete',
            isAdmin: response.data.isAdmin,
            orderId: response.data.order.orderId,
            orderSource: response.data.orderSource,
            orderStats: response.data.orderStats,
            siteBilling: initialBillingInfo,
            siteCartOrderResponse: response.data.order,
            siteShipping,
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_COMPLETED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              customer_id: response.data.customerId ?? '',
              order_id: response.data.order.orderId,
              payment_method: PAYMENT_OPTIONS.KLARNA,
              shipping_method: siteShipping?.shippingOption ?? '',
            },
          );
          setCookie(null, COOKIES.ORDER_ID, response.data.order.orderId + '', {
            maxAge: 86400 * 30,
            path: '/',
            secure: false,
            domain: COOKIES.DOMAIN,
          });
          await redirectOderConfirmation(response.data.order.orderId);
        } else {
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.PAYMENT_DECLINED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              payment_method: PAYMENT_OPTIONS.KLARNA,
              shipping_method: siteShipping?.shippingOption ?? '',
              reason: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
            },
          );
          setShowPaymentError(true);
          setOpenModal(true);
        }
        logger.info('init klarna response', response);
      };
      createCartOrder();
    }
  }, [
    klarnaAuthToken,
    siteCartSummary.siteCart.id,
    userIp,
    cartSummary,
    forterToken,
    initialBillingInfo,
    siteShipping,
    klarnaPayload,
    billingInfo,
    setPaymentData,
    userInfoFormValues.email,
    redirectOderConfirmation,
  ]);

  const handleCloseKlarnaModal = () => {
    setKlarnaModalOpen(false);
    setIsLoading(false);
  };

  const handleConfirm = () => {
    setCrossPageErrorMessage(undefined);
    setPaymentType(PAYMENT_OPTIONS.KATAPULT);
    setOpenModal(false);
  };

  const closeTryKatapultModal = () => {
    setOpenModal(false);
    setIsLoading(false);
    setShowPaymentError(true);
  };

  const handleCancel = () => {
    setIsLoading(false);
    setOpenModal(false);
    setShowPaymentError(true);
  };

  return (
    <div>
      {showPaymentError && (
        <GridItem fullbleed gridColumnL={'start/end'}>
          <div css={paymentErrorStyles.container}>
            <Notification
              id="payment-error"
              subtext={ui('checkout.orderConfirm.genericPaymentErrorMessage')}
              icon={{
                svgId: ICONS.NOTIFICATION,
                type: ICON_IMAGE_TYPE.ICON,
              }}
              title={ui('checkout.paymentType.error.payment')}
              type="Payment Error"
              suppressFromHomePage={false}
              theme={THEME.DARK}
            />
          </div>
        </GridItem>
      )}
      <KlarnaPaymentForm
        initialBillingInfo={initialBillingInfo}
        onContinue={handleContinue}
        cartShipping={siteShipping?.cartShipping}
        shippingOption={siteShipping?.shippingOption}
        isSubmitting={isLoading || isShippingDataLoading}
        hasValidShippingPlace={hasValidShippingPlace}
        hasNoAppointment={hasNoAppointment}
      />
      <KlarnaCheckoutModal
        isOpen={klarnaModalOpen}
        onClose={handleCloseKlarnaModal}
        onPlaceOrder={handlePlaceOrder}
      />
      <TrykatapultModal
        onClose={closeTryKatapultModal}
        isOpen={openModal}
        description={ui('checkout.payments.tryKatapult.description')}
        image={{
          altText: 'katapult image',
          height: 40,
          src: ui('checkout.payments.tryKatapult.logo'),
          type: ICON_IMAGE_TYPE.IMAGE,
          width: 100,
        }}
        subTitle={ui('checkout.payments.tryKatapult.subtitle')}
        title={ui('checkout.payments.tryKatapult.title', {
          type: titleCaseSlug(PAYMENT_OPTIONS.KLARNA),
        })}
      >
        <div css={styles.ctaSection}>
          <Button
            onClick={handleCancel}
            style={BUTTON_STYLE.OUTLINED}
            theme={THEME.LIGHT}
            css={[styles.cta, styles.mr20]}
          >
            {ui('checkout.payments.tryKatapult.notnow')}
          </Button>
          <Button
            onClick={handleConfirm}
            css={styles.katapultBtn}
            id={KATAPULT_BUTTON_ID}
          >
            <Icon name={ICONS.KATAPULT} />
          </Button>
        </div>
      </TrykatapultModal>
    </div>
  );
}

function KlarnaPaymentContainer(props: ServerData) {
  return (
    <KlarnaContextProvider>
      <KlarnaPayment {...props} />
    </KlarnaContextProvider>
  );
}

export default KlarnaPaymentContainer;
