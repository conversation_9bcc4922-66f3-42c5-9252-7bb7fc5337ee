import { SiteCartBillingAddressType } from '~/data/models/SiteCartBilling';
import { SiteCartBillingRequest } from '~/data/models/SiteCartBillingRequest';
import { SiteCartShipping } from '~/data/models/SiteCartShipping';

import { KLARNA_FORM_FIELDS } from '../../checkout.constants';
import { UserInfoFormValues } from '../../Payments/UserInfoForm/UserInfoForm.types';
import { KlarnaFormValues } from './KlarnaPaymentForm/KlarnaPaymentForm';

export const KLARNA_BUTTON_ID = 'klarna_button_id';
export const KLARNA_ASSIST_BUTTON_ID = 'klarna_assist_button_id';

export const mapShippingAddressToKlarnaForm = (
  cartShipping: SiteCartShipping,
): KlarnaFormValues => {
  return {
    [KLARNA_FORM_FIELDS.FIRST_NAME]: cartShipping.firstName,
    [KLARNA_FORM_FIELDS.LAST_NAME]: cartShipping.lastName,
    [KLARNA_FORM_FIELDS.ADDRESS1]: cartShipping.addressLine1,
    [KLARNA_FORM_FIELDS.ADDRESS2]: cartShipping.addressLine2 || '',
    [KLARNA_FORM_FIELDS.STATE]: cartShipping.state,
    [KLARNA_FORM_FIELDS.ZIP]: cartShipping.zip,
    [KLARNA_FORM_FIELDS.EMAIL]: cartShipping.email,
    [KLARNA_FORM_FIELDS.CITY]: cartShipping.city || '',
  };
};

export const mapKlarnaFormValueToCartBillingRequest = (
  formValues: KlarnaFormValues,
  userInfoFormValues: UserInfoFormValues,
): SiteCartBillingRequest => {
  return {
    addressLine1: formValues.addressLine1,
    addressLine2: formValues.addressLine2,
    addressType: SiteCartBillingAddressType.BILLING,
    city: formValues.city,
    firstName: formValues.firstName,
    lastName: formValues.lastName,
    phone: userInfoFormValues.phone,
    state: formValues.state,
    zip: formValues.zip,
  };
};
