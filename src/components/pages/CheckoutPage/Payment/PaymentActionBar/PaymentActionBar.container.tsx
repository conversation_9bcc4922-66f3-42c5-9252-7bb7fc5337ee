import dynamic from 'next/dynamic';

import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';

import { usePaymentContextSelector } from '../PaymentContext/Payment.context';

const PaymentActionBarForMainSite = dynamic(
  () => import('../../Payment/PaymentActionBar/PaymentActionBarForMainSite'),
);

interface PaymentActionBarContainerProps {
  isSimpleSalesToolUser: boolean;
  selectedPaymentMethod: PAYMENT_OPTIONS;
}

export default function PaymentActionBarContainer({
  isSimpleSalesToolUser,
  selectedPaymentMethod,
}: PaymentActionBarContainerProps) {
  const paypalInfoModalOpen = usePaymentContextSelector(
    (v) => v.paypalInfoModalOpen,
  );

  return !paypalInfoModalOpen ? (
    <PaymentActionBarForMainSite
      isSimpleSalesToolUser={isSimpleSalesToolUser}
      selectedPaymentMethod={selectedPaymentMethod}
    />
  ) : null;
}
