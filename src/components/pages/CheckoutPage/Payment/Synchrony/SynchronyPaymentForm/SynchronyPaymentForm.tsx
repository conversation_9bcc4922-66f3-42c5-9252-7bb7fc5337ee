import {
  createRef,
  RefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useReducer,
  useState,
} from 'react';

import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import { useHasChanged } from '~/hooks/useHasChanged';
import { USER_TYPE } from '~/lib/constants/sso';
import { TIME } from '~/lib/constants/time';
import { scrollToRef } from '~/lib/helpers/scroll';
import { ui } from '~/lib/utils/ui-dictionary';

import {
  USER_INFO_FORM_FIELDS,
  UserInfoFormValues,
} from '../../../Payments/UserInfoForm/UserInfoForm.types';
import {
  BillingFieldChangeItem,
  BillingFieldRefs,
  BillingInfoValues,
} from '../../BillingInfoForm/BillingInfo.types';
import BillingInfoForm from '../../BillingInfoForm/BillingInfoForm';
import { billingMustHaveFieldValidState } from '../../BillingInfoForm/BillingInfoForm.util';
import { usePaymentContextSelector } from '../../PaymentContext/Payment.context';
import { useSynchronyContextSelector } from '../Synchrony.context';
import synchronyStyle from '../SynchronyPaymentContainer.styles';
import {
  BILLING_INFO_FIELDS_ARRAY,
  billingInfoElements,
} from './SynchronyPaymentForm.constants';
import {
  generateInitialSynchronyPaymentFormState,
  SYNCHRONY_FORM_ACTION_TYPES,
  SynchronyPaymentBillingInfo,
  synchronyPaymentFormFieldReducer,
  SynchronyPaymentFormValidState,
} from './SynchronyPaymentFormReducer';

export interface RefType {
  validateForm: () => {
    billingInfo: SynchronyPaymentBillingInfo;
    errorState: {
      addressLine1: boolean;
      addressLine2: boolean;
      city: boolean;
      firstName: boolean;
      lastName: boolean;
      phone: boolean;
      phoneType: boolean;
      state: boolean;
      zip: boolean;
    };
    isValid: boolean;
  };
}

export interface SynchronyPaymentFormProps {
  errorStateList?: string | undefined;
  initialBillingInfo: BillingInfoValues;
  isSynchronyPayment: boolean;
  ref: RefObject<RefType | undefined>;
  setErrorStateList: (value: any) => void;
  setIsSynchronyPayment: (value: boolean) => void;
  shippingData: SiteCartShippingResponse | null;
  userType?: USER_TYPE;
}

export default function SynchronyPaymentForm({
  initialBillingInfo,
  shippingData,
  userType,
  setErrorStateList,
  ref,
}: SynchronyPaymentFormProps) {
  const {
    setBillingInfo,
    setIsContinueButtonEnabled,
    setIsSynchronyPayment,
    setSubmitTracker,
    userInfoFormValues,
  } = usePaymentContextSelector((v) => ({
    setBillingInfo: v.setBillingInfo,
    setIsContinueButtonEnabled: v.setIsContinueButtonEnabled,
    setIsSynchronyPayment: v.setIsSynchronyPayment,
    setSubmitTracker: v.setSubmitTracker,
    userInfoFormValues: v.userInfoFormValues,
  }));
  const setSynchronyBillingFormInfo = useSynchronyContextSelector(
    (v) => v.setSynchronyBillingFormInfo,
  );
  const initialValue = generateInitialSynchronyPaymentFormState(
    BILLING_INFO_FIELDS_ARRAY,
    initialBillingInfo,
  );

  const [{ validState, billingInfo, hasErrorState }, dispatch] = useReducer(
    synchronyPaymentFormFieldReducer,
    initialValue,
  );
  const currentTab = useCartUserActionContextSelector((v) => v.currentTab);
  const currentTabChanged = useHasChanged(currentTab);
  const [formElementRefs, setFormElementRefs] = useState<BillingFieldRefs>({});

  const onBillingFieldChange = useCallback(
    (billingFieldChangeArray: Array<BillingFieldChangeItem>) => {
      const valuePayload: Record<string, string | null> = {};
      const validStatePayload: Record<string, boolean> = {};
      const hasErrorStatePayload: Record<string, boolean> = {};

      billingFieldChangeArray.forEach(
        ({ fieldName, fieldValue, fieldValidState }) => {
          valuePayload[fieldName] = fieldValue;
          validStatePayload[fieldName] = fieldValidState;
          hasErrorStatePayload[fieldName] = fieldValidState ? false : true;
        },
      );
      dispatch({
        type: SYNCHRONY_FORM_ACTION_TYPES.UPDATE_BILLING_FIELD,
        payload: valuePayload,
      });
      dispatch({
        type: SYNCHRONY_FORM_ACTION_TYPES.UPDATE_VALID_STATE,
        payload: validStatePayload,
      });
      dispatch({
        type: SYNCHRONY_FORM_ACTION_TYPES.UPDATE_HAS_ERROR_STATE,
        payload: hasErrorStatePayload,
      });
    },
    [],
  );

  useEffect(() => {
    if (!currentTabChanged) {
      return;
    }

    dispatch({
      type: SYNCHRONY_FORM_ACTION_TYPES.UPDATE_HAS_ERROR_STATE,
      payload: initialValue.hasErrorState as Record<string, boolean>,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTab, currentTabChanged]);

  const handleClickBack = useCallback(() => {
    setSubmitTracker(0);
    setIsSynchronyPayment(false);
  }, [setIsSynchronyPayment, setSubmitTracker]);

  const validateUserInfoForm = (formData: UserInfoFormValues) => {
    let isValid = true;
    for (const formKey in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, formKey)) {
        const value = formData[formKey as keyof UserInfoFormValues];
        if (formKey !== USER_INFO_FORM_FIELDS.VEHICLE && !value) {
          isValid = false;
        }
      }
    }
    return isValid;
  };

  const getFormValidate = () => {
    const newHasErrorState = Object.entries(validState).reduce(
      (acc, [key, value]) => {
        if (
          typeof validState[key as keyof SynchronyPaymentFormValidState] !==
          'boolean'
        ) {
          return acc;
        }

        acc[key as keyof SynchronyPaymentFormValidState] = !value;
        return acc;
      },
      {} as SynchronyPaymentFormValidState,
    );

    for (const key of [...BILLING_INFO_FIELDS_ARRAY]) {
      if (
        newHasErrorState[key] &&
        formElementRefs[key] &&
        formElementRefs[key].current
      ) {
        scrollToRef(
          formElementRefs[key] as RefObject<HTMLDivElement | null>,
          TIME.MS400,
          undefined,
          null,
          150,
        );
        break;
      }
    }
    setErrorStateList(
      (
        errorState: Record<string, boolean>,
        newHasErrorState: Record<string, boolean>,
      ) => ({
        ...errorState,
        ...newHasErrorState,
      }),
    );
    dispatch({
      type: SYNCHRONY_FORM_ACTION_TYPES.UPDATE_HAS_ERROR_STATE,
      payload: newHasErrorState as Record<string, boolean>,
    });

    const isValidUserForm = validateUserInfoForm(userInfoFormValues);

    return {
      isValid: Object.values(validState).every((doc) => doc) && isValidUserForm,
      errorState: validState,
      billingInfo,
    };
  };

  useEffect(() => {
    setIsContinueButtonEnabled((prev) => ({
      ...prev,
      [PAYMENT_OPTIONS.CREDIT]: billingMustHaveFieldValidState(validState),
    }));
  }, [setIsContinueButtonEnabled, validState]);

  useEffect(() => {
    if (billingInfo) {
      setBillingInfo(billingInfo);
      setSynchronyBillingFormInfo(billingInfo);
    }
  }, [billingInfo, setBillingInfo, setSynchronyBillingFormInfo]);

  useEffect(() => {
    setErrorStateList(hasErrorState);
    [...BILLING_INFO_FIELDS_ARRAY].forEach((key) => {
      setFormElementRefs((prev) => ({ ...prev, [key]: createRef() }));
    });
  }, [hasErrorState, setErrorStateList]);

  useImperativeHandle(ref, () => ({
    validateForm: getFormValidate,
  }));

  return (
    <GridItem fullbleed>
      <Grid>
        <GridItem fullbleed>
          <button css={synchronyStyle.button} onClick={handleClickBack}>
            <Icon name={ICONS.CHEVRON_LEFT} css={synchronyStyle.backIcon} />
            <h1 css={synchronyStyle.label}>
              {ui('checkout.payments.synchrony.backButton')}
            </h1>
          </button>
        </GridItem>
        <GridItem fullbleed>
          <BillingInfoForm
            billingInfoElements={billingInfoElements}
            billingInfoFieldsArray={BILLING_INFO_FIELDS_ARRAY}
            billingInfo={billingInfo}
            validState={validState}
            onBillingFieldChange={onBillingFieldChange}
            shippingData={shippingData}
            refs={formElementRefs}
            hasErrorState={hasErrorState}
            userType={userType}
            paymentType={PAYMENT_OPTIONS.SYNCHRONY}
          />
        </GridItem>
      </Grid>
    </GridItem>
  );
}
