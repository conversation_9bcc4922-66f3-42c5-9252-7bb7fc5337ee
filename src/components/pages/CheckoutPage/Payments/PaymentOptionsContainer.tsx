import lscache from 'lscache';
import dynamic from 'next/dynamic';
import { useSearchParams } from 'next/navigation';
import { setCookie } from 'nookies';
import { useCallback, useEffect, useRef, useState } from 'react';

import { cookieConfig } from '~/components/modules/Cart/CartSummary.constants';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { usePaymentDataContextSelector } from '~/components/modules/PaymentData/PaymentData.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import { COOKIES } from '~/lib/constants/cookies';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { TIME } from '~/lib/constants/time';
import { scrollToRef } from '~/lib/helpers/scroll';

import { PaymentOptionsContainerProps } from './PaymentOptions.types';
import { OPT_OUT_OPTIONS } from './PaymentOptionsContainer.constants';

const PaymentOptionsContainerForMainSite = dynamic(
  () => import('./PaymentOptionsContainerForMainSite'),
);

export default function PaymentOptionsContainer(
  props: PaymentOptionsContainerProps,
) {
  const { updateLocation, selectVehicle, userPersonalizationData } =
    useUserPersonalizationContextSelector((v) => ({
      selectVehicle: v.selectVehicle,
      updateLocation: v.updateLocation,
      userPersonalizationData: v.userPersonalizationData,
    }));
  const {
    paymentType,
    updateCartSummary,
    setPaymentType,
    setLastPaymentTypeBeforePaypal,
  } = useCartSummaryContextSelector((v) => ({
    paymentType: v.paymentType,
    setLastPaymentTypeBeforePaypal: v.setLastPaymentTypeBeforePaypal,
    setPaymentType: v.setPaymentType,
    updateCartSummary: v.updateCartSummary,
  }));
  const clearCrossPageErrorMessage = usePaymentDataContextSelector(
    (v) => v.clearCrossPageErrorMessage,
  );

  const [showLoader, setShowLoader] = useState(false);
  const titleRef = useRef<HTMLDivElement>(null);

  const searchParams = useSearchParams();
  const userZip =
    searchParams?.get('userZip') || userPersonalizationData?.userLocation?.zip;
  const { siteCart } = props.siteCartSummary;
  const zip = userZip || siteCart.zip;
  const isSiteCartShippingValid = !!props.siteCartShipping;
  const isSiteCartAppointmentValid = !!props.siteCartAppointment;

  const handleChange = useCallback(
    async (paymentType: PAYMENT_OPTIONS) => {
      if (!paymentType) {
        return;
      }

      setPaymentType(paymentType);

      if (OPT_OUT_OPTIONS.includes(paymentType)) {
        await updateCartSummary({
          paymentType,
        } as SiteCartSummaryRequest);
        return;
      }

      await updateCartSummary({
        paymentType,
      } as SiteCartSummaryRequest);

      setLastPaymentTypeBeforePaypal(paymentType);
      setTimeout(() => {
        setShowLoader(false);
        scrollToRef(titleRef, TIME.MS200);
      }, TIME.MS1000);
    },
    [setPaymentType, updateCartSummary, setLastPaymentTypeBeforePaypal],
  );

  useEffect(() => {
    const { siteCartVehicle } = siteCart;

    if (siteCartVehicle) {
      const vehicleData = {
        vehicleMake: siteCartVehicle.make || '',
        vehicleModel: siteCartVehicle.model || '',
        vehicleYear: siteCartVehicle.year || '',
        vehicleTrim: siteCartVehicle.trim || '',
      };
      lscache.set(LOCAL_STORAGE[PROPERTIES.VEHICLE_METADATA], vehicleData);
      selectVehicle(vehicleData);
    }
  }, [selectVehicle, siteCart]);

  useEffect(() => {
    if (zip) {
      updateLocation({ userLocationZip: zip as string });
    }
  }, [zip, updateLocation]);

  useEffect(() => {
    return () => {
      clearCrossPageErrorMessage();
    };
  }, [clearCrossPageErrorMessage]);

  useEffect(() => {
    if (isSiteCartShippingValid) {
      setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
    }
  }, [isSiteCartShippingValid]);

  useEffect(() => {
    if (isSiteCartAppointmentValid) {
      setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
    }
  }, [isSiteCartAppointmentValid]);

  return (
    <PaymentOptionsContainerForMainSite
      {...props}
      ref={titleRef}
      handleChange={handleChange}
      selectedPaymentMethod={paymentType}
      showLoader={showLoader}
    />
  );
}
