import { BORDERS } from '~/lib/constants/borders';
import { MQ } from '~/lib/constants/breakpoints';
import { COLORS } from '~/lib/constants/colors';
import { SPACING } from '~/lib/constants/spacing';
import { StylesMap } from '~/lib/constants/styles.types';
import { fontStyles, typography } from '~/styles/typography.styles';

const styles: StylesMap = {
  affirmIcon: {
    width: 40,
  },
  button: {
    border: `1px solid ${COLORS.LIGHT.GRAY_20}`,
    borderRadius: '15px',
    boxSizing: 'border-box',
    padding: SPACING.SIZE_20,
    width: '100%',
  },
  buttonMain: {
    alignItems: 'center',
    display: 'flex',
  },
  buttonTitle: [
    ...typography.secondaryHeadline,
    {
      marginLeft: SPACING.SIZE_15,
      M: fontStyles(20, 25),
      XL: fontStyles(20, 25),
    },
  ],
  cartSummaryButton: {
    marginTop: SPACING.SIZE_40,
  },
  description: [
    typography.primarySubhead,
    {
      color: COLORS.GLOBAL.PRIMARY,
      display: 'block',
      marginTop: SPACING.SIZE_10,
      textAlign: 'left',
    },
  ],
  fallbackContainer: {
    alignContent: 'center',
    display: 'flex',
    justifyContent: 'center',
    marginTop: SPACING.SIZE_10,
    width: '100%',
  },
  formContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'center',
    marginTop: SPACING.SIZE_40,
    minHeight: '50vh',
  },
  infoTitle: {
    marginBottom: SPACING.SIZE_40,
    display: 'flex',
    flexDirection: 'column',
    'h2 > span': {
      color: COLORS.GLOBAL.PRIMARY,
    },
  },
  infoWrapper: {
    [MQ.M]: {
      paddingTop: SPACING.SIZE_40,
    },
    [MQ.XL]: {
      paddingTop: SPACING.SIZE_40,
    },
    marginBottom: SPACING.SIZE_40,
  },
  linkButton: {
    backgroundColor: COLORS.GLOBAL.TEAL_10,
    borderColor: COLORS.GLOBAL.TEAL,
    color: COLORS.GLOBAL.BLACK,
  },
  linkButtonTop: {
    marginTop: SPACING.SIZE_05,
  },
  loaderContainer: {
    height: 90,
    width: 90,
  },
  paymentError: {
    marginBottom: SPACING.SIZE_40,
  },
  paymentOption: {
    marginBottom: SPACING.SIZE_20,
    '&:last-child': {
      marginBottom: 0,
    },
  },
  paymentSubheader: [
    typography.primarySubhead,
    { color: COLORS.GLOBAL.GRAY_60, opacity: '70%' },
  ],
  resolveExp: [
    typography.smallCopyTight,
    {
      borderBottom: `1px solid ${COLORS.LIGHT.GRAY_70}`,
      color: COLORS.LIGHT.GRAY_70,
      display: 'inline-block',
      marginLeft: SPACING.SIZE_20,
      marginTop: SPACING.SIZE_10,
    },
  ],
  resolveIcon: {
    width: 18,
  },
  resolveLink: [
    typography.labelCopyTight,
    {
      'span > span': {
        borderBottom: BORDERS.SOLID_GRAY_70_1PX,
      },
      padding: `${SPACING.SIZE_10}px ${SPACING.SIZE_20}px`,
    },
  ],
  subTitle: [
    typography.primaryHeadline,
    {
      color: COLORS.GLOBAL.PRIMARY,
    },
  ],
  title: [
    typography.primaryHeadline,
    {
      color: COLORS.GLOBAL.BLACK,
      marginTop: SPACING.SIZE_10,
    },
  ],
  titlePirelli: {
    marginBottom: SPACING.SIZE_10,
  },
  zeroHeight: {
    minHeight: '20vh',
  },
  zeroHeightForFastlane: {
    minHeight: 0,
  },
};

export default styles;
