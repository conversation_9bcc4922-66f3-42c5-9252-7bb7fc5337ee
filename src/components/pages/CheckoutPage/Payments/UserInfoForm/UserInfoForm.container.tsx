import { useMemo } from 'react';

import { getIsTrailerOrCommercial } from '~/components/global/InstallationAppointmentSelectionModal/AppointmentDateTimePicker.utils';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import {
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';

import { PANEL } from '../../checkout.constants';
import { useFastlaneStatusContextSelector } from '../../Payment/Fastlane/FastlaneStatus.context';
import { FASTLANE_STATUS } from '../../Payment/Fastlane/FastlaneStatus.types';
import { usePaymentContextSelector } from '../../Payment/PaymentContext/Payment.context';
import OptionLoadingFallback from '../OptionLoadingFallback/OptionLoadingFallback';
import FastlaneUserInfoFormContainer from './FastlaneUserInfoForm/FastlaneUserInfoForm.container';
import UserInfoForm from './UserInfoForm';
import {
  USER_INFO_FORM_FIELDS,
  UserInfoFormValues,
} from './UserInfoForm.types';

interface UserInfoFormContainerProps {
  cartShipping: SiteCartShippingResponse | null;
  isSimpleSalesToolUser: boolean;
  stableCartSummary: SiteCartSummary;
  userInfoFormValues: UserInfoFormValues;
}

export default function UserInfoFormContainer({
  cartShipping,
  isSimpleSalesToolUser,
  stableCartSummary,
  userInfoFormValues,
}: UserInfoFormContainerProps) {
  const { cartSummary, hasMobileInstall } = useCartSummaryContextSelector(
    (v) => ({
      cartSummary: v.cartSummary,
      hasMobileInstall: v.hasMobileInstall,
    }),
  );
  const {
    activeOption,
    currentTab,
    isShipToMeTabSelected,
    isFedexTabSelected,
  } = useCartUserActionContextSelector((v) => ({
    activeOption: v.activeOption,
    currentTab: v.currentTab,
    isFedexTabSelected: v.isFedexTabSelected,
    isShipToMeTabSelected: v.isShipToMeTabSelected,
  }));
  const userDetail = useUserPersonalizationContextSelector((v) => v.userDetail);
  const fastlaneStatus = useFastlaneStatusContextSelector((v) => v);
  const isSynchronyPayment = usePaymentContextSelector(
    (v) => v.isSynchronyPayment,
  );

  const { siteProducts, email } = stableCartSummary;
  const cartInstallable = cartSummary?.siteProducts.every(
    (product) => product.isInstallable,
  );
  const isFedexSelected =
    isFedexTabSelected && activeOption === ShippingType.FEDEX;
  const hasInstaller =
    (cartInstallable &&
      (currentTab === PANEL.LEFT ||
        (hasMobileInstall && currentTab === PANEL.MID))) ||
    false;
  const initialUserInfoFormValues: UserInfoFormValues = {
    [USER_INFO_FORM_FIELDS.EMAIL]:
      email ||
      cartShipping?.cartShipping.email ||
      userInfoFormValues?.email ||
      userDetail?.username ||
      '',
    [USER_INFO_FORM_FIELDS.PHONE]:
      cartShipping?.cartShipping.phone || userInfoFormValues?.phone || '',
    [USER_INFO_FORM_FIELDS.PHONE_TYPE]:
      cartShipping?.cartShipping.phoneType || PHONE_TYPE.MOBILE,
  };

  const isTrailerOrCommercial = useMemo(
    () => getIsTrailerOrCommercial(siteProducts),
    [siteProducts],
  );

  if (fastlaneStatus === FASTLANE_STATUS.LOADING) {
    return <OptionLoadingFallback />;
  }

  return fastlaneStatus === FASTLANE_STATUS.ENABLED && !isSynchronyPayment ? (
    <FastlaneUserInfoFormContainer
      initialValues={initialUserInfoFormValues}
      isTrailerOrCommercial={isTrailerOrCommercial}
      showVehicle={hasInstaller}
      isShipToMeTabSelected={isShipToMeTabSelected}
      isFedexSelected={isFedexSelected}
      userDetail={userDetail}
    />
  ) : (
    <UserInfoForm
      initialValues={initialUserInfoFormValues}
      isTrailerOrCommercial={isTrailerOrCommercial}
      showVehicle={hasInstaller}
      isShipToMeTabSelected={isShipToMeTabSelected}
      isFedexSelected={isFedexSelected}
      showEmail={
        hasInstaller ||
        isFedexSelected ||
        isShipToMeTabSelected ||
        isSimpleSalesToolUser
      }
      userDetail={userDetail}
    />
  );
}
