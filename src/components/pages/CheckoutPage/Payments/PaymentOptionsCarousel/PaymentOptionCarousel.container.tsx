import dynamic from 'next/dynamic';
import { useCallback, useEffect } from 'react';

import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import {
  ErrorContentType,
  usePaymentDataContextSelector,
} from '~/components/modules/PaymentData/PaymentData.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';

const PaymentOptionsCarouselForMainSite = dynamic(
  () => import('./PaymentOptionsCarouselForMainSite'),
);

interface PaymentOptionsCarouselContainerProps {
  handleChange: (paymentMethod: PAYMENT_OPTIONS) => void;
  hasValidVehicle: boolean;
  selectedPaymentMethod: PAYMENT_OPTIONS;
  totalInCents?: number;
}

export default function PaymentOptionsCarouselContainer({
  handleChange,
  hasValidVehicle,
  selectedPaymentMethod,
  totalInCents,
}: PaymentOptionsCarouselContainerProps) {
  const { currentTab, isShipToMeTabSelected } =
    useCartUserActionContextSelector((v) => ({
      currentTab: v.currentTab,
      isShipToMeTabSelected: v.isShipToMeTabSelected,
    }));
  const crossPageErrorMessage = usePaymentDataContextSelector(
    (v) => v.crossPageErrorMessage,
  );
  const { paymentGroups, companyPaymentGroups } =
    useUserPersonalizationContextSelector((v) => ({
      companyPaymentGroups: v.companyPaymentGroups,
      paymentGroups: v.paymentGroups,
    }));

  const handleTabChange = useCallback(
    async (paymentMethod: PAYMENT_OPTIONS) => {
      await handleChange(paymentMethod);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      currentTab,
      handleChange,
      hasValidVehicle,
      selectedPaymentMethod,
      isShipToMeTabSelected,
    ],
  );

  useEffect(() => {
    if (!crossPageErrorMessage) {
      return;
    }

    if (crossPageErrorMessage.type === ErrorContentType.USER_INFO) {
      return;
    }

    handleTabChange(selectedPaymentMethod ?? PAYMENT_OPTIONS.CREDIT);
  }, [crossPageErrorMessage, handleTabChange, selectedPaymentMethod]);

  return (
    <PaymentOptionsCarouselForMainSite
      totalInCents={totalInCents}
      onChange={handleTabChange}
      selectedPaymentOption={selectedPaymentMethod}
      paymentGroups={paymentGroups}
      companyPaymentGroups={companyPaymentGroups}
    />
  );
}
