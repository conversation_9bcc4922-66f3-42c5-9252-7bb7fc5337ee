import { FullStory } from '@fullstory/browser';
import lscache from 'lscache';
import { useSearchParams } from 'next/navigation';
import { setCookie } from 'nookies';
import { useCallback, useEffect, useState } from 'react';

import { INSTALL_TAB } from '~/components/global/AllInstallationShops/AllInstallationShops.constants';
import { cookieConfig } from '~/components/modules/Cart/CartSummary.constants';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useSiteGlobalsContextSelector } from '~/context/SiteGlobals.context';
import { SHIPPINGSERIVCES } from '~/data/models/SiteCartShippingResponse';
import useRouter from '~/hooks/useRouter';
import useWidgetSource from '~/hooks/useWigetSource';
import { apiUpdateEmailQuote } from '~/lib/api/sales-quote';
import { COOKIES } from '~/lib/constants/cookies';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { isClient } from '~/lib/helpers/browser';
import {
  rudderstackInitialize,
  rudderstackSendIdentifyEvent,
  rudderstackSendTrackEvent,
} from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { mapToProductData } from '~/lib/helpers/rudderstack/transformer';
import { ExtendedEventProperties } from '~/lib/helpers/rudderstack/types';
import { seStorage } from '~/lib/utils/browser-storage';

import { FastlaneContextProvider } from '../Payment/Fastlane/Fastlane.context';
import { useFastlaneStatusContextSelector } from '../Payment/Fastlane/FastlaneStatus.context';
import { FASTLANE_STATUS } from '../Payment/Fastlane/FastlaneStatus.types';
import { PaymentContextProvider } from '../Payment/PaymentContext/Payment.context';
import { PaymentOptionsProps } from './PaymentOptions.types';
import PaymentOptions from './PaymentOptions/PaymentOptions';

function InstallerWidgetUpdateEffect() {
  const { isSourceManufacturerWidget } = useWidgetSource();
  const { isMobile, isTablet } = useSiteGlobalsContextSelector((v) => ({
    isMobile: v.isMobile,
    isTablet: v.isTablet,
  }));
  const setIsCartSummaryModalOpen = useCartSummaryContextSelector(
    (v) => v.setIsCartSummaryModalOpen,
  );
  const searchParams = useSearchParams();
  const installerId = searchParams?.get('installerId');
  const siteUrl = searchParams?.get('siteUrl');
  const widgetSource = searchParams?.get('widgetSource');
  const widgetSourceId = searchParams?.get('widgetSourceId');
  const logoUrl = searchParams?.get('logoUrl');
  const orderSource = searchParams?.get('source');
  const orderSubSource = searchParams?.get('subSource');

  const [mobileState, setMobileState] = useState(false);

  useEffect(() => {
    if (isClient()) {
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.SUB_SOURCE],
        String(orderSubSource),
      );
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
        String(widgetSource),
      );

      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE_ID],
        String(widgetSourceId),
      );

      if (isSourceManufacturerWidget) {
        seStorage.setItem(
          SESSION_STORAGE[SESSION_PROPERTIES.SOURCE],
          String(orderSource),
        );
        seStorage.setItem(
          SESSION_STORAGE[SESSION_PROPERTIES.SUB_SOURCE],
          String(orderSubSource),
        );
      }
    }
    const hasMobileOpen = seStorage.getItem(SESSION_PROPERTIES.HAS_MOBILE_OPEN);

    if (typeof installerId === 'string' && installerId.trim() !== '') {
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_IDS],
        installerId,
      );
    }
    if (typeof siteUrl === 'string' && siteUrl.trim() !== '') {
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_SITE_URL],
        siteUrl.replace('Site', ''),
      );
    }
    if (typeof logoUrl === 'string' && logoUrl.trim() !== '') {
      seStorage.setItem(SESSION_STORAGE[SESSION_PROPERTIES.LOGO_URL], logoUrl);
    }
    if (
      (isMobile || isTablet) &&
      hasMobileOpen != 'true' &&
      mobileState !== true
    ) {
      setIsCartSummaryModalOpen(true);
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.HAS_MOBILE_OPEN],
        'true',
      );
      setMobileState(true);
    }
  }, [
    installerId,
    isMobile,
    isTablet,
    widgetSource,
    orderSource,
    orderSubSource,
    siteUrl,
    logoUrl,
    mobileState,
    setIsCartSummaryModalOpen,
    isSourceManufacturerWidget,
    widgetSourceId,
  ]);

  return null;
}

function PaymentOptionsContainer(props: PaymentOptionsProps) {
  const {
    getSiteInstallersForMobileOptions,
    setHasDefaultSelectMobileInstall,
    setSelectedTab,
  } = useCartSummaryContextSelector((v) => ({
    getSiteInstallersForMobileOptions: v.getSiteInstallersForMobileOptions,
    setHasDefaultSelectMobileInstall: v.setHasDefaultSelectMobileInstall,
    setSelectedTab: v.setSelectedTab,
  }));
  const { isMobile, isTablet } = useSiteGlobalsContextSelector((v) => ({
    isMobile: v.isMobile,
    isTablet: v.isTablet,
  }));
  const fastlaneStatus = useFastlaneStatusContextSelector((v) => v);
  //check if user is navigating from retrieve quote
  const isRetrieveQuote =
    lscache.get(LOCAL_STORAGE[PROPERTIES.RETRIEVE_QUOTE]) === 'yes';
  const isSessionPresent = lscache.get(LOCAL_STORAGE[PROPERTIES.SESSION]);

  const router = useRouter();
  const searchParams = useSearchParams();
  const userID = searchParams?.get('userID');
  const cartIdFromUrl = searchParams?.get('cartId');
  const searchCriteria = searchParams?.get('searchCriteria') || '';
  const widgetSource = searchParams?.get('widgetSource')?.toLowerCase() || '';
  const widgetSourceId =
    searchParams?.get('widgetSourceId')?.toLowerCase() || '';

  const {
    isSourcePirelliWidget,
    isComingFromWidget,
    isSourceInstallerWidget,
    isSourceAffiliateWidget,
    isSourceManufacturerWidget,
  } = useWidgetSource();

  const { siteCart } = props.siteCartSummary;

  const handlePirelli = useCallback(async () => {
    seStorage.setItem(SESSION_STORAGE.REDIRECT_TO_ST_COMPLETED, 'true');
    const { siteCartVehicle, zip, siteProducts, installerDetails } = siteCart;
    await rudderstackInitialize();
    if (isClient()) {
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
        widgetSource,
      );
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE_ID],
        widgetSourceId,
      );
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.PIRELLI_CONTACT],
        widgetSource,
      );
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SEARCH_CRITERIA],
        searchCriteria,
      );
    }
    // Hard coding now till pirelli Version 3 release

    const maxAge10Days = 60 * 60 * 24 * 10;

    setCookie(null, COOKIES.FROM_PIRELLI, 'fromPirelli', {
      ...cookieConfig,
      maxAge: maxAge10Days,
    });
    const hasMobileInstallInSearchParams =
      searchParams?.get('hasMobileInstall');
    const activeShippingType = searchParams?.get('activeShippingType');
    if (
      hasMobileInstallInSearchParams &&
      activeShippingType === SHIPPINGSERIVCES.MOBILEINSTALL
    ) {
      seStorage.setItem(
        SESSION_STORAGE[SESSION_PROPERTIES.PRE_SELECTED_INSTALLER_TYPE],
        hasMobileInstallInSearchParams + '',
      );
      setHasDefaultSelectMobileInstall(true);
      setSelectedTab(INSTALL_TAB.MOBILE);
    }

    if (userID) {
      rudderstackSendIdentifyEvent(userID);
    }

    const product_ids = siteProducts.map((product) => product.productId);
    const quantities = siteProducts.map((product) => product.quantity);
    const currentUrl = window.location.href;
    const referrer = document.referrer;
    const path = window.location.pathname;
    const search = window.location.search;
    const currentTimestamp = new Date().toISOString();
    const WIDGET_UTM_SOURCE = searchParams?.get('Pirelli_UTM_Utm_source') || '';
    const WIDGET_UTM_MEDIUM = searchParams?.get('Pirelli_UTM_Utm_medium') || '';
    const WIDGET_UTM_CAMPAIGN =
      searchParams?.get('Pirelli_UTM_Utm_campaign') || '';
    const WIDGET_UTM_ID = searchParams?.get('Pirelli_UTM_Utm_id') || '';
    const WIDGET_UTM_GCLID = searchParams?.get('Pirelli_UTM_Gclid') || '';
    const continueUrl = searchParams?.get('continueUrl') || '';
    lscache.set(LOCAL_STORAGE[PROPERTIES.CONTINUE_URL], continueUrl);

    const pirelliSessionObject = {
      channel: 'PirelliWidget',
      event: 'PIRELLI_WIDGET_SESSION',
      installer_id: installerDetails?.installerId || '',
      keywords: [],
      name: 'payment/checkout',
      originalTimestamp: currentTimestamp,
      path,
      product_id: product_ids,
      quantity: quantities,
      referrer,
      search,
      sentAt: currentTimestamp,
      type: 'track',
      url: currentUrl,
      WIDGET_UTM_CAMPAIGN,
      WIDGET_UTM_GCLID,
      WIDGET_UTM_ID,
      WIDGET_UTM_MEDIUM,
      WIDGET_UTM_SOURCE,
    };
    rudderstackSendTrackEvent(
      RudderstackTrackEventName.PIRELLI_WIDGET_SESSION,
      pirelliSessionObject,
    );
    const { mobileInstallerList, installerList } =
      await getSiteInstallersForMobileOptions(siteCart, siteCart.id.toString());
    siteProducts.forEach((product) => {
      if (product?.price) {
        let products: ExtendedEventProperties = mapToProductData(
          product,
          siteCart.id.toString(),
          product.quantity,
        );

        products = {
          ...products,
          installerList,
          mobileInstallerList,
          source: 'pirelli',
          vehicleMake: siteCartVehicle?.make || 'Unknown',
          vehicleModel: siteCartVehicle?.model || 'Unknown',
          vehicleTrim: siteCartVehicle?.trim || 'Unknown',
          vehicleYear: siteCartVehicle?.year || 'Unknown',
          zip: zip ?? '',
        };
        rudderstackSendTrackEvent(
          RudderstackTrackEventName.ADD_TO_CART,
          products,
        );
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [siteCart, userID]);

  useEffect(() => {
    if (isComingFromWidget && cartIdFromUrl) {
      setCookie(null, COOKIES.CART_ID, siteCart.id.toString(), cookieConfig);
    }

    if (isSourcePirelliWidget) {
      handlePirelli();
    }
  }, [
    siteCart.id,
    isSourcePirelliWidget,
    isComingFromWidget,
    userID,
    cartIdFromUrl,
    handlePirelli,
    isMobile,
    isTablet,
  ]);

  useEffect(() => {
    async function updateUserSession() {
      await apiUpdateEmailQuote({
        input: {
          cartId: siteCart.id.toString(),
          customerSessionId: isSessionPresent,
        },
        includeUserRegion: true,
        includeUserZip: true,
      });
    }
    if (isRetrieveQuote) {
      if (siteCart.id && siteCart.id !== undefined) {
        updateUserSession();
      } else {
        router.push(ROUTE_MAP[ROUTES.HOME]);
      }
    }
  }, [siteCart.id, isSessionPresent, router, isRetrieveQuote]);

  useEffect(() => {
    if (userID && isSourcePirelliWidget) {
      FullStory('setIdentity', { uid: userID });
    }
  }, [isSourcePirelliWidget, userID]);

  if (fastlaneStatus === FASTLANE_STATUS.LOADING) {
    return null;
  }

  return (
    <div>
      {(isSourceInstallerWidget ||
        isSourceAffiliateWidget ||
        isSourceManufacturerWidget) && <InstallerWidgetUpdateEffect />}
      <PaymentContextProvider
        paymentType={props.selectedPaymentMethod}
        initialBillingInfo={props.initialBillingInfo}
        userIp={props.userIp}
        siteShipping={props.siteCartShipping}
      >
        {fastlaneStatus === FASTLANE_STATUS.ENABLED ? (
          <FastlaneContextProvider>
            <PaymentOptions {...props} />
          </FastlaneContextProvider>
        ) : (
          <PaymentOptions {...props} />
        )}
      </PaymentContextProvider>
    </div>
  );
}

export default PaymentOptionsContainer;
