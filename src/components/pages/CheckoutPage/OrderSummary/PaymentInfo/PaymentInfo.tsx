import { HostedFieldsTokenizePayload } from 'braintree-web/hosted-fields';
import { useMemo } from 'react';

import Button from '~/components/global/Button/Button';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Markdown from '~/components/global/Markdown/Markdown';
import {
  isFastlanePayload,
  usePaymentDataContextSelector,
} from '~/components/modules/PaymentData/PaymentData.context';
import { SiteCartBilling } from '~/data/models/SiteCartBilling';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import { BUTTON_STYLE } from '~/lib/constants/buttons.types';
import { LINK_TYPES } from '~/lib/constants/links';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { THEME } from '~/lib/constants/theme';
import { ui } from '~/lib/utils/ui-dictionary';

import {
  cardIconMap,
  cardIconMapForFastlane,
  CC_TYPE,
} from '../../checkout.util';
import styles from './PaymentInfo.styles';

interface PaymentInfoProps {
  cartBilling: SiteCartBilling;
  hideCTA?: boolean;
}

function PaymentInfo({ cartBilling, hideCTA }: PaymentInfoProps) {
  const { paymentData, synchronyFourDigit } = usePaymentDataContextSelector(
    (v) => ({
      paymentData: v.paymentData,
      synchronyFourDigit: v.synchronyFourDigit,
    }),
  );
  const { paymentType, payload } = paymentData;
  const title = useMemo(() => {
    switch (paymentType) {
      case PAYMENT_OPTIONS.CREDIT: {
        const isFastlane = isFastlanePayload(payload);
        return isFastlane
          ? `•••• •••• •••• ${payload.paymentSource.card.lastDigits}`
          : `•••• •••• •••• ${
              (payload as HostedFieldsTokenizePayload).details.lastFour
            }`;
      }
      case PAYMENT_OPTIONS.PAYPAL:
        return 'Paypal';
      case PAYMENT_OPTIONS.VENMO:
        return 'Venmo';
      case PAYMENT_OPTIONS.APPLE_PAY:
        return 'Apple';
      case PAYMENT_OPTIONS.GOOGLE_PAY:
        return 'Google';
      case PAYMENT_OPTIONS.RESOLVE:
        return 'Resolve';
      case PAYMENT_OPTIONS.SYNCHRONY:
        if (synchronyFourDigit) {
          return `•••• •••• •••• ${synchronyFourDigit}`;
        }
        return 'Continental Synchrony';
      default:
        return '';
    }
  }, [paymentType, payload, synchronyFourDigit]);

  const body = useMemo(() => {
    switch (paymentType) {
      case PAYMENT_OPTIONS.CREDIT:
        return `${cartBilling.addressLine1} ${
          cartBilling.addressLine2 || ''
        }<br/>${cartBilling.city}, ${cartBilling.state} ${cartBilling.zip}`;
      case PAYMENT_OPTIONS.SYNCHRONY:
        return `${cartBilling.addressLine1} ${
          cartBilling.addressLine2 || ''
        }<br/>${cartBilling.city}, ${cartBilling.state} ${cartBilling.zip}`;
      case PAYMENT_OPTIONS.PAYPAL:
        return `${cartBilling.firstName} ${cartBilling.lastName}<br/>${cartBilling.phone}`;
      case PAYMENT_OPTIONS.VENMO:
        return `${cartBilling.firstName} ${cartBilling.lastName}<br/>${cartBilling.phone}`;
      case PAYMENT_OPTIONS.APPLE_PAY:
        return `${cartBilling.firstName} ${cartBilling.lastName}<br/>${cartBilling.phone}`;
      case PAYMENT_OPTIONS.GOOGLE_PAY:
        return `${cartBilling.firstName} ${cartBilling.lastName}<br/>${cartBilling.phone}`;
      case PAYMENT_OPTIONS.KATAPULT:
        return ui('checkout.orderSummary.katapultInfo');
      default:
        return '';
    }
  }, [paymentType, cartBilling]);

  const iconName = useMemo(() => {
    switch (paymentType) {
      case PAYMENT_OPTIONS.CREDIT: {
        const isFastlane = isFastlanePayload(payload);
        if (isFastlane) {
          const cardType = payload.paymentSource.card.brand;
          return cardIconMapForFastlane[cardType] ?? ICONS.CREDIT_CARD;
        } else {
          const cardType = (payload as HostedFieldsTokenizePayload).details
            .cardType as CC_TYPE;
          return cardIconMap[cardType] ?? ICONS.CREDIT_CARD;
        }
      }
      case PAYMENT_OPTIONS.PAYPAL:
        return ICONS.PAYPAL;
      case PAYMENT_OPTIONS.VENMO:
        return ICONS.VENMO;
      case PAYMENT_OPTIONS.APPLE_PAY:
        return ICONS.APPLE;
      case PAYMENT_OPTIONS.GOOGLE_PAY:
        return ICONS.GOOGLE;
      case PAYMENT_OPTIONS.RESOLVE:
        return ICONS.RESOLVE;
      case PAYMENT_OPTIONS.KATAPULT:
        return ICONS.KATAPULT;
      case PAYMENT_OPTIONS.SYNCHRONY:
        return ICONS.CONTINENTAL_SYNCHRONY;
      case PAYMENT_OPTIONS.KLARNA:
        return ICONS.KLARNA;
      default:
        return ICONS.CREDIT_CARD;
    }
  }, [payload, paymentType]);

  return (
    <div css={styles.root}>
      <div css={styles.paymentInfo}>
        <Icon name={iconName} css={styles.icon} />
        <div css={styles.details}>
          <h3 css={styles.title}>{title}</h3>
          {paymentType !== PAYMENT_OPTIONS.RESOLVE && (
            <Markdown css={styles.body}>{body}</Markdown>
          )}
        </div>
      </div>
      {!hideCTA && (
        <Button
          href={ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]}
          as={LINK_TYPES.A}
          style={BUTTON_STYLE.OUTLINED}
          theme={THEME.LIGHT}
          css={styles.button}
          data-component="payment_info_edit"
          id="payment_info_edit"
        >
          {ui('checkout.orderSummary.edit')}
        </Button>
      )}
    </div>
  );
}

export default PaymentInfo;
