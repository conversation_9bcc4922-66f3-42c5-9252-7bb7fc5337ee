import { GooglePaymentTokenizePayload } from 'braintree-web/google-payment';
import { HostedFieldsTokenizePayload } from 'braintree-web/hosted-fields';
import { parseCookies, setCookie } from 'nookies';
import { useState } from 'react';

import Car from '~/components/global/Car/Car';
import { Cars } from '~/components/global/Car/Car.enums';
import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import Markdown from '~/components/global/Markdown/Markdown';
import Scenery from '~/components/global/Scenery/Scenery';
import { Sceneries } from '~/components/global/Scenery/Scenery.types';
import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import {
  isFastlanePayload,
  isResolvePayload,
  usePaymentDataContextSelector,
  VenmoPayload,
} from '~/components/modules/PaymentData/PaymentData.context';
import Layout from '~/components/pages/CheckoutPage/Layout/Layout';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import {
  OrderPaymentType,
  SiteCartOrderRequestWithoutSessionId,
} from '~/data/models/SiteCartOrderRequest';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import useBreakpoints from '~/hooks/useBreakpoints';
import useRouter from '~/hooks/useRouter';
import { apiCreateSiteCartOrder } from '~/lib/api/checkout/cart-order';
import { COOKIES } from '~/lib/constants/cookies';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { PROPERTIES, SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import GA from '~/lib/helpers/analytics';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { seStorage } from '~/lib/utils/browser-storage';
import { ui } from '~/lib/utils/ui-dictionary';

import Feedback from '../Feedback/Feedback';
import Footer from '../Footer/Footer';
import Header from '../Header/Header';
import OrderSummary from './OrderSummary';
import { ServerData } from './OrderSummary.types';
import styles from './OrderSummaryContainer.styles';
import PaymentError from './PaymentError/PaymentError';
import StickyBar from './StickyBar/StickyBar';

function OrderSummaryContainer({
  siteShipping,
  siteBilling,
  userIp,
}: ServerData) {
  const router = useRouter();
  const {
    cartSummary,
    setIsCartSummaryModalOpen,
    setCustomerId,
    setIsOpenTimeChangeModalOnCheckout,
  } = useCartSummaryContextSelector((v) => ({
    cartSummary: v.cartSummary,
    setIsCartSummaryModalOpen: v.setIsCartSummaryModalOpen,
    setCustomerId: v.setCustomerId,
    setIsOpenTimeChangeModalOnCheckout: v.setIsOpenTimeChangeModalOnCheckout,
  }));
  const cartAppointment = useCartShippingContextSelector(
    (v) => v.cartAppointment,
  );
  const { paymentData, clearPaymentData } = usePaymentDataContextSelector(
    (v) => ({
      paymentData: v.paymentData,
      clearPaymentData: v.clearPaymentData,
    }),
  );
  const isDealerTire = useUserPersonalizationContextSelector(
    (v) => v.isDealerTire,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [disableContinueButton, setDisableContinueButton] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>();
  const [errorTitle, setErrorTitle] = useState<string | undefined>();
  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] || null;
  const { lessThan } = useBreakpoints();

  const handleDropdown = () => {
    setIsCartSummaryModalOpen(true);
  };

  const placeOrder = async () => {
    setIsLoading(true);
    setErrorMessage(undefined);
    setErrorTitle(undefined);
    if (cartAppointment === null) {
      setIsOpenTimeChangeModalOnCheckout(true);
      setIsLoading(false);
      return;
    }

    if (
      paymentData &&
      paymentData.payload &&
      paymentData.paymentType &&
      storedCartId
    ) {
      const forterToken = cookies[COOKIES.FORTER] || null;
      const { payload, paymentType } = paymentData;
      let input: SiteCartOrderRequestWithoutSessionId;
      if (isResolvePayload(payload)) {
        input = {
          cartId: storedCartId,
          paymentType: OrderPaymentType.RESOLVE,
          resolve: {
            chargeId: payload.chargeId,
          },
          userAgent: window.navigator.userAgent,
          userIp,
        };
      } else {
        const isFastlane = isFastlanePayload(payload);
        input = {
          braintree: {
            cardType: paymentType,
            paymentMethodNonce: isFastlane ? payload.id : payload.nonce,
            last4Digits: null,
          },
          cartId: storedCartId,
          forterToken,
          paymentType: OrderPaymentType.BRAINTREE,
          source:
            seStorage.getItem(SESSION_STORAGE[PROPERTIES.SOURCE]) ?? undefined,
          subSource:
            seStorage.getItem(SESSION_STORAGE[PROPERTIES.SUB_SOURCE]) ??
            undefined,
          userAgent: window.navigator.userAgent,
          userIp,
          widgetSource:
            seStorage.getItem(SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE]) ??
            undefined,
        };
        if (paymentType === PAYMENT_OPTIONS.CREDIT) {
          input.braintree.last4Digits = isFastlane
            ? payload.paymentSource.card.lastDigits
            : (payload as HostedFieldsTokenizePayload).details.lastFour;
        }
        if (paymentType === PAYMENT_OPTIONS.GOOGLE_PAY) {
          input.braintree.last4Digits = (
            payload as GooglePaymentTokenizePayload
          ).details.lastFour;
        }
        if (paymentType === PAYMENT_OPTIONS.VENMO) {
          input.braintree.deviceData = (payload as VenmoPayload).deviceData;
        }
      }
      try {
        const response = await apiCreateSiteCartOrder({
          input,
        });
        if (response.isSuccess) {
          GA.addToDataLayer({
            cartSummary,
            customerId: response.data.customerId,
            emailMatchesExistingAccount:
              response.data.emailMatchesExistingAccount,
            event: 'isCheckoutComplete',
            isAdmin: response.data.isAdmin,
            orderId: response.data.order.orderId,
            orderSource: response.data.orderSource,
            orderStats: response.data.orderStats,
            siteBilling,
            siteCartOrderResponse: response.data.order,
            siteShipping,
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_COMPLETED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              customer_id: response.data.customerId ?? '',
              order_id: response.data.order.orderId,
              payment_method: paymentData.paymentType ?? '',
              shipping_method: siteShipping?.shippingOption ?? '',
            },
          );
          setCookie(null, COOKIES.ORDER_ID, response.data.order.orderId + '', {
            maxAge: 86400 * 30,
            path: '/',
            secure: false,
            domain: COOKIES.DOMAIN,
          });
          setCustomerId(response.data.customer.id);
          await router.replace(
            ROUTE_MAP[ROUTES.ORDER_CONFIRMATION] +
              '/' +
              response.data.order.orderId,
          );
        } else {
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.PAYMENT_DECLINED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              payment_method: paymentData.paymentType ?? '',
              reason: ui('checkout.orderConfirm.genericPaymentErrorMessage'),
              shipping_method: siteShipping?.shippingOption ?? '',
            },
          );
          if (response.error.statusCode === 539) {
            setErrorMessage(ui('checkout.orderConfirm.payment539ErrorMessage'));
            setErrorTitle(ui('checkout.orderConfirm.payment539ErrorTitle'));
            setDisableContinueButton(true);
          } else {
            setErrorMessage(
              ui('checkout.orderConfirm.genericPaymentErrorMessage'),
            );
            setErrorTitle(ui('checkout.paymentType.error.payment'));
          }
          window.scroll({
            top: 0,
            behavior: 'smooth',
          });
        }
      } finally {
        clearPaymentData();
      }
    }
    setIsLoading(false);
  };

  if (!cartSummary) {
    return null;
  }

  const SummaryHeader = (
    <div css={styles.summaryItem}>
      <h3 css={styles.title}>{ui('checkout.orderSummary.title')}</h3>
    </div>
  );

  function renderHeader() {
    return (
      <div css={!lessThan.XL && styles.largeLayoutLeft}>
        <div
          css={[
            lessThan.XL ? styles.headerContainer : styles.headerContainerLarge,
          ]}
        >
          {!lessThan.XL && (
            <div css={styles.liveChatContainer}>
              <Header />
            </div>
          )}
          <div css={styles.header}>
            <h3 css={styles.headerTitle}>
              {ui('checkout.orderSummary.headerTitle')}
            </h3>
            <h4 css={styles.headerSubtitle}>
              {ui('checkout.orderSummary.headerSubtitle')}
            </h4>
          </div>
          <Scenery sceneryID={Sceneries['scenery--urban']} />
          <div css={styles.headerCar}>
            <Car solid scaleAcrossBreakpoints carId={Cars['car--audi-a6']} />
          </div>
        </div>
        {!lessThan.XL && (
          <div css={styles.footerContainer}>
            <Feedback customContainerStyles={styles.footerContainer} />
            <Footer />
          </div>
        )}
      </div>
    );
  }

  function renderOrderSummary() {
    return lessThan.XL ? (
      <Grid css={styles.titleSection}>
        <GridItem fullbleed gridColumnL="2/14">
          <PaymentError errorMessage={errorMessage} />
        </GridItem>
        <GridItem gridColumn="1/7" gridColumnM="2/8" gridColumnL="2/14">
          {SummaryHeader}
          <OrderSummary siteShipping={siteShipping} siteBilling={siteBilling} />
          <Markdown css={styles.termsOfSale}>
            {isDealerTire
              ? ui('checkout.termsOfSaleDealerTire')
              : ui('checkout.termsOfSale')}
          </Markdown>
        </GridItem>
      </Grid>
    ) : (
      <div css={[styles.largeLayoutRight]}>
        <PaymentError errorMessage={errorMessage} errorTitle={errorTitle} />
        {SummaryHeader}
        <OrderSummary
          siteShipping={siteShipping}
          siteBilling={siteBilling}
          removeGridForLarge
        />
        <Markdown css={styles.termsOfSale}>
          {isDealerTire
            ? ui('checkout.termsOfSaleDealerTire')
            : ui('checkout.termsOfSale')}
        </Markdown>
      </div>
    );
  }
  function renderContent() {
    return (
      <div css={styles.root}>
        {renderHeader()}
        {renderOrderSummary()}
        <StickyBar
          totalPrice={Number(cartSummary?.totalInCents || 0)}
          onContinue={placeOrder}
          onClickDropdown={handleDropdown}
          isDisabled={isLoading || disableContinueButton}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return lessThan.XL ? <Layout>{renderContent()}</Layout> : renderContent();
}

export default OrderSummaryContainer;
