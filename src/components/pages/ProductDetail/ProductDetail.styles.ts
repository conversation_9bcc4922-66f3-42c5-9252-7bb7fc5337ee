import { NAV_HEIGHT } from '~/components/modules/Nav/Nav.constants';
import { MQ, MQ_MMW } from '~/lib/constants/breakpoints';
import { COLORS } from '~/lib/constants/colors';
import { SPACING } from '~/lib/constants/spacing';
import { StylesMap } from '~/lib/constants/styles.types';
import { TIME } from '~/lib/constants/time';
import { Z_INDEX } from '~/lib/constants/zindex';
import { getColumnsCalc } from '~/lib/utils/grid';

function defaultSpacing(rule: string, offset = 0) {
  return {
    [rule]: SPACING.SIZE_60 + offset,

    [MQ.M]: {
      [rule]: SPACING.SIZE_80 + offset,
    },
  };
}

const styles: StylesMap = {
  anchorBar: {
    marginTop: SPACING.SIZE_35,

    [MQ.M]: {
      marginTop: SPACING.SIZE_50,
    },
    [MQ.L]: {
      paddingLeft: SPACING.SIZE_50,
      paddingRight: SPACING.SIZE_50,
    },
  },
  breadcrumbs: {
    marginBottom: SPACING.SIZE_27,
    marginTop: 0,
    [MQ.M]: {
      marginTop: 'unset',
      marginBottom: SPACING.SIZE_30,
      position: 'relative',
      top: '-30px',
    },
    [MQ.L]: {
      marginBottom: 0,
      top: '0px',
    },
    [MQ.XL]: {
      top: '-20px',
    },
  },
  colorStripe: {
    '::before': {
      '@media(min-width: 1600px)': {
        transform: 'rotate(-7deg) translate(0%, 60%)',
        width: '5000px',
      },
      '@media(min-width: 2500px)': {
        transform: 'rotate(-7deg) translate(0%, 70%)',
      },
      '@media(min-width: 3000px)': {
        transform: 'rotate(-6deg) translate(0%, 55%)',
      },
      '@media(min-width: 4000px)': {
        transform: 'rotate(-5deg) translate(0%, 70%)',
      },
      [MQ.S]: {
        height: '40%',
      },
      [MQ.M]: {
        height: '45%',
      },
      [MQ.L]: {
        height: '25%',
      },
      [MQ.XL]: {
        height: '40%',
      },
      content: '""',
      height: '40%',
      left: '-1000px',
      opacity: '0.2',
      position: 'absolute',
      top: '50%',
      transform: 'rotate(-7deg) translate(0%, 50%)',
      transformOrigin: 'top left',
      width: '3000px',
    },
    '@media(min-width: 1600px)': {
      height: '500px',
    },
    [MQ.S]: {
      height: '100% ',
      top: 0,
    },
    [MQ.L]: {
      top: 30,
    },
    [MQ.XL]: {
      height: 'calc(100% - 30px)',
      left: 0,
      right: 'unset',
      top: 30,
      width: 'calc(100% - 30px)',
    },
    backgroundColor: 'transparent',
    height: '375px',
    margin: 'auto',
    overflow: 'hidden',
    position: 'absolute',
    right: 0,
    top: -80,
    width: '100%',
    zIndex: '-1',
  },
  countDownWrapper: {
    margin: '0 20px 40px',
    [MQ.M]: {
      margin: '0 40px 40px',
    },
    [MQ.L]: {
      margin: '0 0px 40px',
    },
  },
  desktopMaxWidth: {
    [MQ.XL]: {
      margin: '0 auto',
      maxWidth: '1320px',
    },
  },
  detailsSection: [
    defaultSpacing('paddingBottom', -SPACING.SIZE_20),
    defaultSpacing('paddingTop'),
    {
      backgroundColor: COLORS.GLOBAL.BLACK,

      '> div:not(:first-of-type):not(:last-of-type)':
        defaultSpacing('marginTop'),
    },
  ],
  featuredRecirculation: {
    ...defaultSpacing('marginTop'),
    '.product-carousel-container': {
      '.swiper-wrapper': {
        '.recirculation-item': {
          ':first-of-type': {
            [MQ.L]: {
              marginLeft: getColumnsCalc({
                breakpoint: 'L',
                columns: 1,
                includeExtraGutter: true,
                includeContainerMargin: true,
              }),
            },

            [MQ.XL]: {
              marginLeft: getColumnsCalc({
                breakpoint: 'XL',
                columns: 1,
                includeExtraGutter: true,
                includeContainerMargin: true,
              }),
            },
          },

          ':last-of-type': {
            [MQ.L]: {
              marginRight: getColumnsCalc({
                breakpoint: 'L',
                columns: 1,
                includeExtraGutter: true,
                includeContainerMargin: true,
              }),
            },

            [MQ.XL]: {
              marginRight: getColumnsCalc({
                breakpoint: 'XL',
                columns: 1,
                includeExtraGutter: true,
                includeContainerMargin: true,
              }),
            },
          },
        },
      },
    },
  },
  gridContainer: {
    marginTop: 30,
  },
  heroCategoryImage: {
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    height: '400px',
    maskImage: 'radial-gradient(ellipse, black 30%, transparent 75%)',
    [MQ.S]: {
      height: '560px',
    },
    [MQ.M]: {
      height: '430px',
      maskImage: 'radial-gradient(ellipse, black 30%, transparent 70%)',
    },
    [MQ.L]: {
      height: '590px',
    },
    opacity: 0.2,
    width: '100%',
    zIndex: '-2',
  },
  heroCategoryImageM: {
    [MQ.S]: {
      display: 'none',
    },
    [MQ.M]: {
      display: 'block',
    },
    [MQ.L]: {
      display: 'none',
    },
  },
  heroCategoryImageS: {
    [MQ.S]: {
      display: 'block',
    },
    [MQ.M]: {
      display: 'none',
    },
    [MQ.L]: {
      display: 'none',
    },
  },
  heroCategoryImageXL: {
    [MQ.S]: {
      display: 'none',
    },
    [MQ.M]: {
      display: 'none',
    },
    [MQ.L]: {
      display: 'block',
    },
  },
  hiddenCategoryImage: {
    display: 'none',
  },
  imageContainer: {
    marginLeft: -SPACING.SIZE_60,
    position: 'absolute',
    top: -70,
    width: '100vw',
    [MQ.S]: {
      marginLeft: -SPACING.SIZE_20,
    },
    [MQ.M]: {
      marginLeft: -SPACING.SIZE_60,
      top: 0,
    },
    [MQ.L]: {
      left: '0px',
      marginLeft: -60,
      width: 'calc(100vw - 80px)',
    },
  },
  lastTable: {
    [MQ.M]: {
      paddingBottom: SPACING.SIZE_60,
    },
  },
  middleSection: {
    gridColumn: '1/15',
  },
  plaTechSpecs: [
    defaultSpacing('paddingTop'),
    defaultSpacing('paddingBottom', -SPACING.SIZE_20),

    {
      background: COLORS.GLOBAL.BLACK,
    },
    {
      [MQ.L]: [defaultSpacing('marginTop')],
    },
  ],
  productDetailContainer: {
    position: 'relative',
  },
  productInfo: {
    [MQ.L]: {
      backdropFilter: 'blur(3px)',
      // backgroundImage: 'url(/static/assets/pdp/pdp_background.svg)',
      backgroundPosition: '-22px 0',
      backgroundRepeat: 'no-repeat',
      backgroundSize: '102%',
      zIndex: 0,
    },
    [MQ.XL]: {
      paddingRight: SPACING.SIZE_20,
    },
  },
  productInfoBG: {
    [MQ.L]: {
      boxShadow: '5px 13px 15px rgba(0, 0, 0, 0.15)',
      marginLeft: '0px',
      marginTop: '90px',
      paddingBottom: '40px',
      width: '95%',
    },
    [MQ.XL]: {
      paddingBottom: '60px',
      marginLeft: '0px',
      width: '98%',
    },
    '@media(min-width: 1420px)': {
      marginTop: '100px',
    },
    '@media(min-width: 1800px)': {
      marginLeft: '0px',
      marginTop: '120px',
      width: '98%',
    },
    '@media(min-width: 2200px)': {
      marginTop: '140px',
    },
    '@media(min-width: 2500px)': {
      marginTop: '170px',
    },
  },
  purchaseIncludes: [
    defaultSpacing('marginTop'),
    {
      [MQ.M]: {
        marginTop: SPACING.SIZE_60,
      },

      [MQ.L]: {
        marginTop: SPACING.SIZE_80,
      },
    },
  ],
  root: {
    opacity: 1,
    transition: `opacity ${TIME.MS300}ms ease`,
    [MQ.L]: {
      paddingTop: SPACING.SIZE_40,
    },
  },
  rootLoading: {
    opacity: 1,
  },
  saleBannerContainer: {
    margin: '-7px 0 36px',
    [MQ.L]: {
      margin: '20px 0 49px',
    },
  },
  saleBannerContainerUpdate: {
    borderRadius: SPACING.SIZE_15,
    margin: '-7px auto 35px',
    maxWidth: '90%',
    overflow: 'hidden',
    [MQ.L]: {
      margin: '20px auto 49px',
    },
    [MQ.XL]: {
      margin: '20px auto 49px',
      maxWidth: '1320px',
    },
  },
  searchCTAContainer: {
    [MQ.M]: {
      paddingTop: NAV_HEIGHT.M,
    },
    [MQ.L]: {
      paddingTop: NAV_HEIGHT.L + 20,
    },
    [MQ.XL]: {
      paddingTop: NAV_HEIGHT.XL + 20,
    },
    marginBottom: SPACING.SIZE_40,
  },
  shopTiresBySpace: {
    [MQ_MMW.S]: {
      paddingTop: SPACING.SIZE_160 - 10,
    },
  },
  shopWithConfidence: [
    defaultSpacing('marginTop'),
    {
      marginBottom: SPACING.SIZE_60,
      [MQ.M]: {
        marginBottom: SPACING.SIZE_120,
      },
      [MQ.XL]: {
        marginBottom: SPACING.SIZE_120,
      },
    },
  ],
  shopWithConfidenceUpdate: {
    marginTop: 0,
    [MQ.XL]: {
      marginTop: 40,
    },
  },
  stickyBar: {
    bottom: 0,
    position: 'sticky',
    zIndex: Z_INDEX.MODAL - 20,
  },
  stickyBarUpdate: {
    '> div': {
      height: 80,
    },
  },
  stripeContainer: {
    marginLeft: -SPACING.SIZE_60,
    position: 'absolute',
    width: '100vw',
    zIndex: 0,
    [MQ.S]: {
      height: 320,
      marginLeft: -SPACING.SIZE_20,
    },
    [MQ.M]: {
      height: 370,
      marginLeft: -SPACING.SIZE_60,
    },
    [MQ.L]: {
      height: 460,
      left: 0,
      marginLeft: 0,
    },
    [MQ.XL]: {
      height: 460,
      left: '0px',
      marginLeft: 0,
      maxWidth: 1320,
      width: 'calc(100vw - 80px)',
    },
  },
  tireImage: {
    marginBottom: SPACING.SIZE_10,
    width: '100%',

    [MQ.M]: {
      marginBottom: SPACING.SIZE_15,
    },

    [MQ.L]: {
      marginBottom: 0,
    },
  },
  topLeftSectionContainer: {
    [MQ.L]: {
      position: 'sticky',
      top: SPACING.SIZE_40,
    },
  },
  topLeftSectionUpdate: {
    [MQ.XL]: {
      gridColumn: 'wrapper-start/8',
    },
  },
};

export default styles;
