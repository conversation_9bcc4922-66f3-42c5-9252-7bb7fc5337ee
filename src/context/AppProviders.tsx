'use client';

import { type ReactNode } from 'react';
import { SWRConfig } from 'swr';

import { InstallationShopDetailsModalContextProvider } from '~/components/global/InstallationShopDetails/InstallationShopDetailsModal.context';
import { CartShippingContextProvider } from '~/components/modules/Cart/CartShipping.context';
import { CartSummaryContextProvider } from '~/components/modules/Cart/CartSummary.context';
import { CartUserActionContextProvider } from '~/components/modules/Cart/CartUserAction.context';
import { SearchContextProvider } from '~/components/modules/Search/Search.context';
import { SearchModalContextProvider } from '~/components/modules/Search/SearchModal.context';
import { SelectedInstallerContextProvider } from '~/components/modules/SelectedInstaller/SelectedInstaller.context';
import { TireSnapModalContextProvider } from '~/components/modules/TireSnap/TireSnapModal.context';
import { type SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import { type SiteCartSummary } from '~/data/models/SiteCartSummary';
import { type SiteGlobals } from '~/data/models/SiteGlobals';
import { type SiteMenuContextProps } from '~/data/models/SiteMenu';
import { type SiteNotificationList } from '~/data/models/SiteNotificationsList';

import { ComponentErrorBoundaryCounterContextProvider } from './ComponentErrorBoundaryCounter.context';
import { ContactEmailModalContextProvider } from './ContactEmailModal.context';
import { FooterContextProvider } from './Footer.context';
import { GlobalsContextProvider } from './Globals.context';
import { GlobalToastContextProvider } from './GlobalToast.context';
import { GuidedQuestionsModalProvider } from './GuidedQuestionsModal.context';
import { ModalContextProvider } from './Modal.context';
import { NavContextProvider } from './Nav.context';
import PaymentRelatedContext from './PaymentRelatedContext/PaymentRelatedContext';
import { RecentlyViewedTiresProvider } from './RecentlyViewedTires.context';
import { RouterContextProvider } from './Router.context';
import { SimpleScoreCardModalContextProvider } from './SimpleScoreCardModal.context';
import { SiteGlobalsContextProvider } from './SiteGlobals.context';
import { SiteMenuContextProvider } from './SiteMenu.context';
import { SiteNotificationsContextProvider } from './SiteNotifications.context';
import AllThirdPartyScriptsContext from './ThirdPartyScriptsContext/AllThirdPartyScriptsContext';
import { UserLocationContextProvider } from './UserLocation.context';
import { UserPersonalizationContextProvider } from './UserPersonalization.context';
import { UserZipManualEntryModalProvider } from './UserZipManualEntryModal.context';
import { WidgetConfigProvider } from './WidgetConfig.context';

interface Props {
  cartShipping?: SiteCartShippingResponse;
  cartSummary?: SiteCartSummary;
  children: ReactNode;
  hostUrl?: string | null;
  isOTS?: string;
  isSimpleShop?: string;
  siteGlobalsContextValue?: SiteGlobals;
  siteMenuContextValue?: SiteMenuContextProps;
  siteNotificationContextValue?: SiteNotificationList;
  userAgentType?: string;
  userIp?: string | null;
  vwoExperimentId1?: string;
  vwoExperimentId2?: string;
  vwoExperimentId3?: string;
  vwoExperimentId4?: string;
  vwoExperimentId5?: string;
}

// Container to wrap _app.tsx in context providers.
// Not all providers need to go here; only ones used throughout the app
function AppProviders({
  children,
  hostUrl,
  isSimpleShop,
  isOTS,
  siteGlobalsContextValue,
  siteMenuContextValue,
  siteNotificationContextValue,
  userAgentType,
  userIp,
  cartShipping,
  cartSummary,
  vwoExperimentId2,
  vwoExperimentId1,
  vwoExperimentId3,
  vwoExperimentId4,
  vwoExperimentId5,
}: Props) {
  return (
    <SWRConfig value={{ revalidateOnFocus: false, shouldRetryOnError: false }}>
      <GlobalsContextProvider
        value={{
          hostUrl,
          isOTS,
          isSimpleShop,
          userAgentType,
          userIp,
          vwoExperimentId1,
          vwoExperimentId2,
          vwoExperimentId3,
          vwoExperimentId4,
          vwoExperimentId5,
        }}
      >
        <WidgetConfigProvider>
          <SiteGlobalsContextProvider
            value={siteGlobalsContextValue}
            userAgentType={userAgentType}
          >
            <ComponentErrorBoundaryCounterContextProvider>
              <AllThirdPartyScriptsContext>
                <NavContextProvider>
                  <SiteMenuContextProvider value={siteMenuContextValue}>
                    <UserPersonalizationContextProvider>
                      <UserLocationContextProvider>
                        <FooterContextProvider>
                          <SearchContextProvider>
                            <SearchModalContextProvider>
                              <CartSummaryContextProvider value={cartSummary}>
                                <CartShippingContextProvider
                                  value={cartShipping}
                                >
                                  <CartUserActionContextProvider>
                                    <RouterContextProvider>
                                      <PaymentRelatedContext>
                                        <TireSnapModalContextProvider>
                                          <GlobalToastContextProvider>
                                            <InstallationShopDetailsModalContextProvider>
                                              <SiteNotificationsContextProvider
                                                value={
                                                  siteNotificationContextValue
                                                }
                                              >
                                                <ModalContextProvider>
                                                  <ContactEmailModalContextProvider>
                                                    <RecentlyViewedTiresProvider>
                                                      <SimpleScoreCardModalContextProvider>
                                                        <UserZipManualEntryModalProvider>
                                                          <SelectedInstallerContextProvider>
                                                            <GuidedQuestionsModalProvider>
                                                              {children}
                                                            </GuidedQuestionsModalProvider>
                                                          </SelectedInstallerContextProvider>
                                                        </UserZipManualEntryModalProvider>
                                                      </SimpleScoreCardModalContextProvider>
                                                    </RecentlyViewedTiresProvider>
                                                  </ContactEmailModalContextProvider>
                                                </ModalContextProvider>
                                              </SiteNotificationsContextProvider>
                                            </InstallationShopDetailsModalContextProvider>
                                          </GlobalToastContextProvider>
                                        </TireSnapModalContextProvider>
                                      </PaymentRelatedContext>
                                    </RouterContextProvider>
                                  </CartUserActionContextProvider>
                                </CartShippingContextProvider>
                              </CartSummaryContextProvider>
                            </SearchModalContextProvider>
                          </SearchContextProvider>
                        </FooterContextProvider>
                      </UserLocationContextProvider>
                    </UserPersonalizationContextProvider>
                  </SiteMenuContextProvider>
                </NavContextProvider>
              </AllThirdPartyScriptsContext>
            </ComponentErrorBoundaryCounterContextProvider>
          </SiteGlobalsContextProvider>
        </WidgetConfigProvider>
      </GlobalsContextProvider>
    </SWRConfig>
  );
}

export default AppProviders;
