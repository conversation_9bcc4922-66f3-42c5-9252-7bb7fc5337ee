import { usePathname } from 'next/navigation';
import { type ReactNode } from 'react';

import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';

import { useRouterContextSelector } from '../Router.context';
import BrainTreePaymentRelatedProvider from './BrainTreePaymentRelatedProvider';

interface PaymentRelatedContextProps {
  children: ReactNode;
}

function PaymentRelatedContext({ children }: PaymentRelatedContextProps) {
  const pathname = usePathname() ?? '';
  const isCheckoutPage = pathname.startsWith('/checkout');
  const isFirstLoadedPage = useRouterContextSelector(
    (v) => v.isFirstLoadedPage,
  );
  const isCartSummaryValid = useCartSummaryContextSelector(
    (v) => !!v.cartSummary,
  );

  return !isFirstLoadedPage || isCheckoutPage || isCartSummaryValid ? (
    <BrainTreePaymentRelatedProvider>
      {children}
    </BrainTreePaymentRelatedProvider>
  ) : (
    <>{children}</>
  );
}

export default PaymentRelatedContext;
