import { SsoErrorResponse } from '~/data/models/SsoUser';
import { UserPersonalization } from '~/data/models/UserPersonalization';
import logger from '~/lib/helpers/logger';
import { extractAafesCIDFromCookie } from '~/lib/utils/aafes/aafes-auth';
import {
  parseSSOUseridFromRequest,
  parseUserRegionFromRequest,
  parseUserZipFromRequest,
} from '~/lib/utils/request';
import { extractSpecificSSOCookie, getSSOBaseURL } from '~/lib/utils/sso';

import { COOKIES } from '../constants/cookies';
import { getCurrentUsertime } from '../utils/string';
import {
  FetchError,
  FetchErrorCodes,
  isDataError1,
  isDataError2,
} from './FetchError';
import { ApiRequest, AsyncResponse } from './index.types';

export class GlobalRef<T> {
  private readonly sym: symbol;

  constructor(uniqueName: string) {
    this.sym = Symbol.for(uniqueName);
  }

  get value() {
    return (global as any)[this.sym] as T;
  }

  set value(value: T) {
    (global as any)[this.sym] = value;
  }
}

const authorizationExpiration = new GlobalRef<Date | null>(
  'newAuthorizationExpiration',
);
const authorizationHeader = new GlobalRef<string | null>(
  'newAuthorizationToken',
);
let authorizationFunction: (() => Promise<void>) | null = null;
let ssoUserIdFunction:
  | ((headerCookie: string) => Promise<string | undefined>)
  | null = null;
let urlBase = '';

let currentUserPersonalization: UserPersonalization = {
  gaClientId: null,
  userLocation: null,
};

const ErrorByStatus: Record<number, string> = {
  400: FetchErrorCodes.BadRequest,
  401: FetchErrorCodes.Unauthorized,
  403: FetchErrorCodes.Forbidden,
  404: FetchErrorCodes.NotFound,
};

export function buildUrl(
  path: string,
  params: Record<string, string | string[]>,
  query: Record<string, string>,
) {
  const pathWithParams = path.replace(/\{([^}]+)\}/g, (_, key: string) => {
    return encodeURIComponent(params[key].toString());
  });

  const searchParams = new URLSearchParams(query).toString();
  return pathWithParams + (searchParams ? `?${searchParams}` : '');
}

interface FetchProps<U> {
  authorizationFunctionRetriesLeft?: number;
  customBaseUrl?: string;
  customHeader?: Record<string, string>;
  endpoint: string;
  formBody?: Record<string, string>;
  includeAuthorization?: boolean;
  includeUserRegion?: boolean;
  includeUserSSOUid?: boolean;
  includeUserTime?: boolean;
  includeUserVwo?: boolean;
  includeUserZip?: boolean;
  includeWidgetId?: boolean;
  jsonBody?: U;
  method: RequestInit['method'];
  params?: Record<string, string | string[]>;
  query?: Record<string, string>;
  request?: ApiRequest;
  signal?: AbortSignal;
  ssoToken?: string;
}

export async function fetchFromSSO<T, U = never>({
  jsonBody,
  formBody,
  endpoint,
  signal,
  params = {},
  query = {},
  method,
  includeAuthorization,
  ssoToken,
}: FetchProps<U>): Promise<AsyncResponse<T>> {
  try {
    const global = typeof globalThis !== 'undefined' ? globalThis : window;
    const headers: Record<string, string> = {};
    let body;
    if (includeAuthorization) {
      headers.Authorization = `Bearer ${ssoToken}`;
    }

    if (jsonBody) {
      headers['Content-Type'] = 'application/json';
      body = JSON.stringify(jsonBody);
    } else if (formBody) {
      headers['Content-Type'] = 'application/x-www-form-urlencoded';
      body = new URLSearchParams(formBody).toString();
    }

    let response: Response;
    const urlBase = getSSOBaseURL();
    const url = buildUrl(`${urlBase}${endpoint}`, params, query);

    try {
      response = await global.fetch(url, {
        body,
        headers,
        method,
        signal,
      });
    } catch (error) {
      throw new FetchError(
        FetchErrorCodes[(error as FetchError).name] ||
          FetchErrorCodes.NetworkError,
        (error as FetchError).message,
      );
    }

    let data: T | null = null;
    try {
      data = (await response.json()) as T;
    } catch (error) {
      if (response.status !== 204 && response.status !== 404) {
        logger.error('fetchFromSSO parse json error: ', error);
        throw new FetchError(
          FetchErrorCodes.InvalidJson,
          (error as FetchError).message,
        );
      }
    }

    if (response.status > 300) {
      throw new FetchError(
        FetchErrorCodes.OtherError,
        (data as unknown as SsoErrorResponse).detail,
        response.status,
      );
    }

    const extractedData = data as T;
    return {
      data: extractedData,
      isSuccess: true,
      statusCode: extractedData ? 200 : 204,
    };
  } catch (e) {
    return {
      error: {
        code: (e as FetchError).code,
        message: (e as FetchError).message,
        statusCode: (e as FetchError).statusCode || 404,
      },
      isSuccess: false,
    };
  }
}

export async function fetch<T, U = never>({
  authorizationFunctionRetriesLeft = 1,
  jsonBody,
  formBody,
  endpoint,
  includeAuthorization,
  includeUserRegion,
  includeUserZip,
  includeWidgetId,
  includeUserSSOUid,
  includeUserTime,
  includeUserVwo,
  method,
  params = {},
  query = {},
  signal,
  customBaseUrl = '',
  request,
  customHeader,
}: FetchProps<U>): Promise<T> {
  const headerCookie = request?.headers?.cookie ?? '';
  const isSimpleShop = await extractSpecificSSOCookie(
    COOKIES.SIMPLE_SHOP_ID,
    String(headerCookie),
  );
  const otsAccountTypeCompany = await extractSpecificSSOCookie(
    COOKIES.OTS_ACCOUNT_TYPE_COMPANY,
    String(headerCookie),
  );
  const otsWidgetId = await extractSpecificSSOCookie(
    COOKIES.OTS_WIDGET_ID,
    String(headerCookie),
  );
  let ssoUid = parseSSOUseridFromRequest(request);
  const userZip =
    parseUserZipFromRequest(request) ??
    currentUserPersonalization?.userLocation?.zip;
  const userRegion =
    parseUserRegionFromRequest(request) ??
    currentUserPersonalization.userLocation?.region;

  const global = typeof globalThis !== 'undefined' ? globalThis : window;
  if (urlBase === '' && customBaseUrl === '') {
    throw new FetchError(
      FetchErrorCodes.UrlBaseNotConfigured,
      'fetch API not configured',
    );
  }

  if (includeUserRegion && userRegion) {
    query.userRegion = String(userRegion);
  }

  if (includeUserZip && userZip) {
    query.userZip = userZip;
  }

  const headers: Record<string, string> = { ...customHeader };
  const hasAuthorizationHeader = Boolean(authorizationHeader.value);

  const isAuthorizationExpired =
    authorizationExpiration.value !== null &&
    Date.now() >= +authorizationExpiration.value;

  if (
    includeAuthorization &&
    authorizationFunction &&
    (!hasAuthorizationHeader || isAuthorizationExpired)
  ) {
    await authorizationFunction();
  }

  if (includeAuthorization && authorizationHeader.value) {
    headers.Authorization = authorizationHeader.value;
  }

  if (typeof request?.headers['x-user-session-id'] === 'string') {
    query.userSessionId = request.headers['x-user-session-id'];
  }

  if (includeUserSSOUid && ssoUserIdFunction && headerCookie && !ssoUid) {
    ssoUid = await ssoUserIdFunction(headerCookie);
  }

  if (includeUserSSOUid && ssoUid) {
    query.ssoUid = ssoUid;
  }

  if (includeUserVwo && !otsAccountTypeCompany) {
    const vwo_user = await extractSpecificSSOCookie(
      COOKIES.VWO,
      String(headerCookie),
    );

    if (vwo_user) {
      query.vwo_user = vwo_user;
    } else if (typeof request?.headers['x-vwo-user'] === 'string') {
      query.vwo_user = request.headers['x-vwo-user'];
    }
  }

  if (includeUserTime) {
    query.userTime = getCurrentUsertime();
  }

  if (typeof request?.headers['x-user-time'] === 'string') {
    query.userTime = request.headers['x-user-time'];
  }

  if (isSimpleShop && !otsAccountTypeCompany) {
    query.channel = 'widget';
  }
  if (headerCookie && !otsAccountTypeCompany) {
    const aafesCID = await extractAafesCIDFromCookie(String(headerCookie));
    if (aafesCID) {
      headers['Aafes-Customer-Id'] = aafesCID;
      query.channel = 'AAFES';
    }
  }
  if (otsAccountTypeCompany) {
    query.accountTypeCompany = otsAccountTypeCompany;
    delete query.vwo_user;
    delete query.channel;
  }
  if (otsWidgetId) {
    query.widgetId = otsWidgetId;
  }
  let body;

  if (jsonBody) {
    headers['Content-Type'] = 'application/json';
    if (includeUserSSOUid && ssoUid) {
      body = JSON.stringify({ ...jsonBody, ssoUid });
    } else {
      body = JSON.stringify(jsonBody);
    }
  } else if (formBody) {
    headers['Content-Type'] = 'application/x-www-form-urlencoded';
    body = new URLSearchParams(formBody).toString();
  }

  let response: Response;
  try {
    const url = buildUrl(
      `${customBaseUrl ? customBaseUrl : urlBase}${endpoint}`,
      params,
      query,
    );
    response = await global.fetch(url, {
      body,
      headers,
      method,
      signal,
    });
  } catch (error) {
    logger.error('fetch api call error: ', error);

    throw new FetchError(
      FetchErrorCodes[(error as FetchError).name] ||
        FetchErrorCodes.NetworkError,
      (error as FetchError).message,
    );
  }

  let data: T | null = null;
  try {
    data = (await response.json()) as T;
  } catch (error) {
    if (
      response.status !== 204 &&
      response.status !== 404 &&
      response.status !== 401
    ) {
      logger.error('fetch parse json error: ', error);
      const fetchError = new FetchError(
        FetchErrorCodes.InvalidJson,
        (error as FetchError).message,
      );
      fetchError.statusCode = response.status;
      throw fetchError;
    }
  }

  if (response.status < 200 || response.status >= 300) {
    if (
      response.status === 401 &&
      includeAuthorization &&
      authorizationFunction &&
      authorizationFunctionRetriesLeft > 0
    ) {
      await authorizationFunction();

      return fetch<T, U>({
        authorizationFunctionRetriesLeft: authorizationFunctionRetriesLeft - 1,
        endpoint,
        formBody,
        includeAuthorization,
        includeUserRegion,
        includeUserTime,
        includeUserVwo,
        includeUserZip,
        includeWidgetId,
        jsonBody,
        method,
        params,
        query,
        signal,
      });
    }

    const error = new FetchError(
      ErrorByStatus[response.status] ?? FetchErrorCodes.ServerError,
      `Invalid response status ${response.status}`,
    );
    error.data = data;
    error.statusCode = response.status;

    // avoid logging 404s to keep logs clean
    if (response.status !== 404) {
      logger.error('fetch api call response error: ', error);
    }

    throw error;
  }

  return data as T;
}

export async function fetchWithErrorHandling<T, U = never>(
  props: FetchProps<U>,
): Promise<AsyncResponse<T>> {
  try {
    const data = await fetch<T, U>(props);
    return {
      data,
      isSuccess: true,
      statusCode: data ? 200 : 204,
    };
  } catch (e) {
    let message = (e as FetchError).message;
    const data = (e as FetchError).data;
    if (isDataError1(data)) {
      message = data.errorMessage;
    }
    if (isDataError2(data)) {
      message = data.error.message;
    }
    return {
      error: {
        code: (e as FetchError).code,
        message,
        statusCode: (e as FetchError).statusCode || 404,
      },
      isSuccess: false,
    };
  }
}

export function fetchGetUserPersonalization() {
  return currentUserPersonalization;
}

export function fetchSetAuthorizationHeader(
  newAuthorizationHeader: string | null,
) {
  authorizationHeader.value = newAuthorizationHeader;
}

export function fetchSetAuthorizationExpiration(
  newAuthorizationExpiration: Date | null,
) {
  authorizationExpiration.value = newAuthorizationExpiration;
}

export function fetchSetAuthorizationFunction(
  newAuthorizationFunction: (() => Promise<void>) | null,
) {
  authorizationFunction = newAuthorizationFunction;
}

export function fetchSetSSOUseridFunction(
  newSSOUserIdFunction:
    | ((headerCookie: string) => Promise<string | undefined>)
    | null,
) {
  ssoUserIdFunction = newSSOUserIdFunction;
}

export function fetchSetAuthorizationToken(
  newAuthorizationToken: string | null,
  expiresOn: Date | null,
) {
  fetchSetAuthorizationHeader(`Bearer ${newAuthorizationToken}`);
  fetchSetAuthorizationExpiration(expiresOn);
}

export function fetchSetUrlBase(newUrlBase: string) {
  urlBase = newUrlBase;
}

export function fetchSetUserPersonalization(
  userPersonalization: UserPersonalization,
) {
  currentUserPersonalization = userPersonalization;
}
