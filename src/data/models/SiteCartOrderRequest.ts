import {
  AffirmOrderRequest,
  KatapultOrderRequest,
} from '~/data/models/SiteOrderRequest/AffirmOrderRequest';
import { ResolveOrderRequest } from '~/data/models/SiteOrderRequest/ResolveOrderRequest';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';

import { CartKlarnaPayload } from './CartKlarnaResponse';
import { SynchronyPaymentRequest } from './SiteOrderRequest/SynchronyPaymentRequest';

export enum OrderPaymentType {
  AFFIRM = 'affirm',
  BRAINTREE = 'braintree',
  KATAPULT = 'katapult',
  KLARNA = 'klarna',
  RESOLVE = 'resolve',
  SYNCHRONY = 'synchrony',
}

export type SiteCartOrderRequest =
  | CartOrderWithBraintreeRequest
  | CartOrderWithKlarnaRequest
  | CartOrderWithKatapultRequest
  | AffirmOrderRequest
  | KatapultOrderRequest
  | SynchronyPaymentRequest
  | ResolveOrderRequest;

export type SiteCartOrderRequestWithoutSessionId =
  | Omit<CartOrderWithBraintreeRequest, 'sessionId'>
  | Omit<CartOrderWithKlarnaRequest, 'sessionId'>
  | Omit<CartOrderWithKatapultRequest, 'sessionId'>
  | Omit<AffirmOrderRequest, 'sessionId'>
  | Omit<KatapultOrderRequest, 'sessionId'>
  | Omit<SynchronyPaymentRequest, 'sessionId'>
  | Omit<ResolveOrderRequest, 'sessionId'>;

export interface CartOrderWithBraintreeRequest {
  braintree: {
    cardType: PAYMENT_OPTIONS;
    deviceData?: string;
    last4Digits: string | null;
    paymentMethodNonce: string;
  };
  cartId: string;
  forterToken: string | null;
  paymentType: OrderPaymentType.BRAINTREE;
  sessionId: string;
  source?: string;
  subSource?: string;
  userAgent: string;
  userIp?: string | null;
  widgetSource?: string;
}

export interface CartOrderWithAffirmRequest {
  affirm: {
    checkout_token: string;
  };
  cartId: string;
  forterToken: string | null;
  paymentType: OrderPaymentType.AFFIRM;
  sessionId: string;
  userAgent: string;
  userIp?: string | null;
  widgetSource?: string;
}

export interface CartOrderWithKatapultRequest {
  cartId: string;
  forterToken: string | null;
  katapult: {
    customer_id: string;
    uid: string;
    zibby_id: string;
  };
  paymentType: OrderPaymentType.KATAPULT;
  sessionId: string;
  userAgent: string;
  userIp?: string | null;
  widgetSource?: string;
}

export interface CartOrderWithKlarnaRequest {
  cartId: string;
  forterToken: string | null;
  klarna: {
    authorizationToken: string;
    cartDetails: CartKlarnaPayload;
  };
  paymentType: OrderPaymentType.KLARNA;
  sessionId: string;
  source?: string;
  subSource?: string;
  userAgent: string;
  userIp?: string | null;
  widgetSource?: string;
}
