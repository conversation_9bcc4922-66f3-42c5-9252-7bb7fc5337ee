export enum IDMEVerifiedStatus {
  MILITARY = 'military',
  NULL = '',
  WECARE = 'wecare',
}

export interface SiteCartSummaryRequest {
  /**
   *
   * @type string | null | undefined
   * @memberof SiteCartSummaryRequest
   */
  email?: string | null;
  /**
   *
   * @type boolean
   * @memberof SiteCartSummaryRequest
   */
  excludeShipping?: boolean;
  /**
   *
   * @type boolean
   * @memberof SiteCartSummaryRequest
   */
  fromSimpleSalesTool?: boolean;
  /**
   *
   * @type IDMEVerifiedStaStatus | undefined
   * @memberof SiteCartSummaryRequest
   */
  idMeVerifiedStatus?: IDMEVerifiedStatus;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  installerId?: string | null;
  /**
   *
   * @type boolean | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  isGroupRoadHazard?: boolean;
  /**
   *
   * @type boolean
   * @memberof SiteCartSummaryRequest
   */
  isNewItem?: boolean;
  /**
   *
   * @type boolean | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  isRoadHazard?: boolean;
  /**
   *
   * @type string | null | undefined
   * @memberof SiteCartSummaryRequest
   */
  itemId?: string | null;
  /**
   *
   * @type number | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  itemQuantity?: number | null;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  paymentType?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  preShippingSelection?: string;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  promoCode?: string | null;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  removeInstallerId?: string | null;
  /**
   *
   * @type string | null | undefined
   * @memberof SiteCartSummaryRequest
   */
  removePromoCode?: string | null;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  sessionId?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  ssoUid?: string;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  subSource?: string | null;
  trcOneYearUuid?: string;
  trcThreeYearUuid?: string;
  trcUuid?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  vehicleDescription?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  vehicleMake?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  vehicleModel?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  vehicleTrim?: string;
  /**
   *
   * @type string | undefined
   * @memberof SiteCartSummaryRequest
   */
  vehicleYear?: string;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  widgetSource?: string | null;
  /**
   *
   * @type string | undefined | null
   * @memberof SiteCartSummaryRequest
   */
  widgetSourceId?: string | null;
}
